import 'dart:developer';

/// Utility class for handling Khmer text processing and validation
class KhmerTextUtils {
  // Khmer Unicode range: U+1780 to U+17FF
  static const String khmerUnicodePattern = r'[\u1780-\u17FF]';
  
  /// Check if text contains Khmer characters
  static bool containsKhmerText(String text) {
    return RegExp(khmerUnicodePattern).hasMatch(text);
  }
  
  /// Extract only Khmer text from mixed content
  static String extractKhmerText(String text) {
    final khmerMatches = RegExp(khmerUnicodePattern).allMatches(text);
    return khmerMatches.map((match) => match.group(0)).join();
  }
  
  /// Extract only Latin/English text from mixed content
  static String extractLatinText(String text) {
    // Remove Khmer characters and clean up
    return text.replaceAll(RegExp(khmerUnicodePattern), ' ')
               .replaceAll(RegExp(r'\s+'), ' ')
               .trim();
  }
  
  /// Log text analysis for debugging
  static void analyzeText(String text, {String? label}) {
    final hasKhmer = containsKhmerText(text);
    final khmerText = extractKhmerText(text);
    final latinText = extractLatinText(text);
    
    log('=== Text Analysis ${label != null ? '($label)' : ''} ===');
    log('Original: "$text"');
    log('Has Khmer: $hasKhmer');
    log('Khmer part: "$khmerText"');
    log('Latin part: "$latinText"');
    log('Length: ${text.length} chars');
  }
  
  /// Common Khmer words found on Cambodia ID cards (for reference)
  static const Map<String, String> commonKhmerWords = {
    'ប្រទេសកម្ពុជា': 'Cambodia',
    'អត្តសញ្ញាណប័ណ្ណ': 'Identity Card',
    'ឈ្មោះ': 'Name',
    'ភេទ': 'Gender',
    'ថ្ងៃខែឆ្នាំកំណើត': 'Date of Birth',
    'ទីកន្លែងកំណើត': 'Place of Birth',
    'អាសយដ្ឋាន': 'Address',
    'សញ្ជាតិ': 'Nationality',
    'ប្រុស': 'Male',
    'ស្រី': 'Female',
  };
  
  /// Try to translate common Khmer words to English
  static String translateCommonWords(String khmerText) {
    String result = khmerText;
    commonKhmerWords.forEach((khmer, english) {
      result = result.replaceAll(khmer, english);
    });
    return result;
  }
}
