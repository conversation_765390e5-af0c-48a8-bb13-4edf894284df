import 'dart:io';
import 'package:camera/camera.dart';
import 'package:flutter/foundation.dart';
import 'package:path_provider/path_provider.dart';
import 'package:path/path.dart' as path;
import 'package:image_picker/image_picker.dart';

class CameraService {
  CameraController? controller;
  List<CameraDescription>? cameras;
  bool _isInitialized = false;
  bool _isUsingFallback = false;
  String? _lastError;

  bool get isInitialized => _isInitialized;
  bool get isUsingFallback => _isUsingFallback;
  String? get lastError => _lastError;

  // Initialize camera
  Future<void> initialize() async {
    try {
      // Reset state
      _isInitialized = false;
      _isUsingFallback = false;
      _lastError = null;

      // Attempt to get available cameras
      debugPrint('Attempting to get available cameras...');
      cameras = await availableCameras();

      if (cameras == null || cameras!.isEmpty) {
        throw Exception('No cameras available');
      }

      debugPrint(
        'Found ${cameras!.length} camera(s). Using camera: ${cameras![0].name}',
      );

      // Use the first camera (usually the back camera)
      // Optimized settings for OCR text recognition
      controller = CameraController(
        cameras![0],
        ResolutionPreset.veryHigh, // Higher resolution for better text clarity
        enableAudio: false,
        imageFormatGroup: ImageFormatGroup.jpeg,
      );

      debugPrint('Initializing camera controller...');
      await controller!.initialize();
      debugPrint('Camera controller initialized successfully');

      // Apply OCR-optimized camera settings
      await _applyOCROptimizedSettings();

      _isInitialized = true;
    } catch (e) {
      _lastError = e.toString();
      debugPrint('Error initializing camera: $e');
      _isInitialized = false;

      // Fall back to image picker if camera initialization fails
      _isUsingFallback = true;
      debugPrint('Falling back to image picker for camera functionality');

      rethrow;
    }
  }

  // Apply camera settings optimized for OCR text recognition
  Future<void> _applyOCROptimizedSettings() async {
    if (controller == null || !controller!.value.isInitialized) return;

    try {
      // Set focus mode to auto for sharp text capture
      await controller!.setFocusMode(FocusMode.auto);
      debugPrint('Set focus mode to auto for OCR');

      // Set exposure mode to auto for consistent lighting
      await controller!.setExposureMode(ExposureMode.auto);
      debugPrint('Set exposure mode to auto for OCR');

      // Set flash mode to auto (will help in low light conditions)
      await controller!.setFlashMode(FlashMode.auto);
      debugPrint('Set flash mode to auto for OCR');
    } catch (e) {
      debugPrint('Warning: Could not apply some OCR settings: $e');
      // Continue even if some settings fail - camera will still work
    }
  }

  // Take a picture and return the image file
  Future<File?> takePicture() async {
    try {
      if (_isUsingFallback) {
        // Use image picker as fallback
        debugPrint('Using image picker fallback to take picture');
        final ImagePicker picker = ImagePicker();
        final XFile? photo = await picker.pickImage(
          source: ImageSource.camera,
          imageQuality: 95, // Higher quality for better OCR accuracy
          maxWidth: 2048, // Optimal resolution for text recognition
          maxHeight: 2048,
        );

        if (photo == null) {
          debugPrint('No image selected from image picker');
          return null;
        }

        return File(photo.path);
      }

      if (!_isInitialized || controller == null) {
        throw Exception('Camera not initialized');
      }

      debugPrint('Taking picture with camera controller...');
      final XFile photo = await controller!.takePicture();
      debugPrint('Picture taken successfully: ${photo.path}');

      // Create a unique filename
      final Directory appDir = await getApplicationDocumentsDirectory();
      final String timestamp = DateTime.now().millisecondsSinceEpoch.toString();
      final String filePath = path.join(appDir.path, 'image_$timestamp.jpg');

      // Save the image to the new path
      final File savedImage = File(filePath);
      await savedImage.writeAsBytes(await File(photo.path).readAsBytes());
      debugPrint('Image saved to: ${savedImage.path}');

      return savedImage;
    } catch (e) {
      _lastError = e.toString();
      debugPrint('Error taking picture: $e');

      // Try fallback if not already using it
      if (!_isUsingFallback) {
        _isUsingFallback = true;
        debugPrint('Switching to image picker fallback after camera error');
        return takePicture(); // Recursive call using fallback
      }

      return null;
    }
  }

  // Get diagnostic information about camera state
  Map<String, dynamic> getDiagnosticInfo() {
    return {
      'isInitialized': _isInitialized,
      'isUsingFallback': _isUsingFallback,
      'lastError': _lastError,
      'cameraCount': cameras?.length ?? 0,
      'currentCamera':
          cameras?.isNotEmpty == true
              ? {
                'name': cameras![0].name,
                'lensDirection': cameras![0].lensDirection.toString(),
              }
              : null,
      'controllerInitialized': controller?.value.isInitialized ?? false,
    };
  }

  // Dispose camera resources
  void dispose() {
    if (controller != null) {
      debugPrint('Disposing camera controller');
      controller!.dispose();
      controller = null;
    }
    _isInitialized = false;
  }
}
