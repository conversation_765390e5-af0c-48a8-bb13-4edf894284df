import 'dart:io';
import 'package:flutter/material.dart';
import 'package:camera/camera.dart';
import 'package:image_picker/image_picker.dart';
import '../services/camera_service.dart';
import '../services/ocr_service.dart';
import '../models/id_card_data_model.dart';
import '../widgets/app_button.dart';
import '../widgets/cambodia_id_card_overlay.dart';
import '../utils/permission_handler.dart';
import 'loan_evaluation_form_screen.dart';

/// A screen for scanning Cambodia ID cards with a specialized overlay
class CambodiaIDCardScanScreen extends StatefulWidget {
  const CambodiaIDCardScanScreen({super.key});

  @override
  State<CambodiaIDCardScanScreen> createState() =>
      _CambodiaIDCardScanScreenState();
}

class _CambodiaIDCardScanScreenState extends State<CambodiaIDCardScanScreen>
    with WidgetsBindingObserver {
  final CameraService _cameraService = CameraService();
  final OcrService _ocrService = OcrService();
  final ImagePicker _imagePicker = ImagePicker();

  bool _isLoading = true;
  bool _isProcessing = false;
  File? _capturedImage;
  IDCardData? _extractedData;
  String _errorMessage = '';
  bool _isFrontSide = true; // Track if we're capturing front or back side

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addObserver(this);
    _initializeCamera();
  }

  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);
    _cameraService.dispose();
    _ocrService.dispose();
    super.dispose();
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    // Handle app lifecycle changes
    if (state == AppLifecycleState.inactive) {
      _cameraService.dispose();
    } else if (state == AppLifecycleState.resumed) {
      _initializeCamera();
    }
  }

  Future<void> _initializeCamera() async {
    try {
      setState(() {
        _isLoading = true;
        _errorMessage = '';
      });

      // Check camera permission first
      final hasPermission = await PermissionHandler.checkCameraPermission(
        context,
      );
      if (!hasPermission) {
        if (mounted) {
          setState(() {
            _isLoading = false;
            _errorMessage = 'Camera permission denied';
          });
        }
        return;
      }

      // Log permission diagnostics
      final permissionDiagnostics =
          await PermissionHandler.getPermissionDiagnostics();
      debugPrint('Permission diagnostics: ${permissionDiagnostics.toString()}');
      debugPrint('Initializing camera...');
      await _cameraService.initialize();

      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _isLoading = false;
          _errorMessage = 'Failed to initialize camera: $e';
        });
      }
    }
  }

  Future<void> _takePicture() async {
    try {
      setState(() {
        _isProcessing = true;
      });

      final File? imageFile = await _cameraService.takePicture();
      if (imageFile == null) {
        throw Exception('Failed to capture image');
      }

      setState(() {
        _capturedImage = imageFile;
      });

      await _processImage(imageFile);
    } catch (e) {
      setState(() {
        _errorMessage = 'Error taking picture: $e';
        _isProcessing = false;
      });
    }
  }

  Future<void> _pickImageFromGallery() async {
    try {
      setState(() {
        _isProcessing = true;
      });

      final XFile? pickedFile = await _imagePicker.pickImage(
        source: ImageSource.gallery,
      );

      if (pickedFile == null) {
        setState(() {
          _isProcessing = false;
        });
        return;
      }

      final File imageFile = File(pickedFile.path);
      setState(() {
        _capturedImage = imageFile;
      });

      await _processImage(imageFile);
    } catch (e) {
      setState(() {
        _errorMessage = 'Error picking image: $e';
        _isProcessing = false;
      });
    }
  }

  Future<void> _processImage(File imageFile) async {
    try {
      final IDCardData extractedData = await _ocrService.extractIDCardData(
        imageFile,
      );

      setState(() {
        _extractedData = extractedData;
        _isProcessing = false;
      });
    } catch (e) {
      setState(() {
        _errorMessage = 'Error processing image: $e';
        _isProcessing = false;
      });
    }
  }

  void _proceedToLoanForm() {
    if (_extractedData != null) {
      Navigator.push(
        context,
        MaterialPageRoute(
          builder:
              (context) =>
                  LoanEvaluationFormScreen(idCardData: _extractedData!),
        ),
      );
    }
  }

  void _resetState() {
    setState(() {
      _capturedImage = null;
      _extractedData = null;
      _errorMessage = '';
    });
  }

  void _toggleSide() {
    setState(() {
      _isFrontSide = !_isFrontSide;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(
          _isFrontSide ? 'Cambodia ID Card - Front' : 'Cambodia ID Card - Back',
        ),
        actions: [
          IconButton(
            icon: const Icon(Icons.flip_camera_android),
            onPressed: _toggleSide,
            tooltip: 'Switch between front and back side',
          ),
        ],
      ),
      body:
          _isLoading
              ? const Center(child: CircularProgressIndicator())
              : _buildBody(),
    );
  }

  Widget _buildBody() {
    if (_errorMessage.isNotEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Text(
              'Error: $_errorMessage',
              style: const TextStyle(color: Colors.red),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 20),
            AppButton(
              label: 'Try Again',
              onPressed: () {
                setState(() {
                  _errorMessage = '';
                  _initializeCamera();
                });
              },
            ),
          ],
        ),
      );
    }

    if (_capturedImage != null) {
      return _buildResultView();
    }

    return _buildCameraView();
  }

  Widget _buildCameraView() {
    if (_cameraService.isUsingFallback) {
      // Fallback view when camera is not available
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(Icons.camera_alt, size: 64, color: Colors.grey),
            const SizedBox(height: 16),
            const Text(
              'Camera is not available. Please use the gallery option.',
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 16),
            AppButton(
              label: 'Pick from Gallery',
              icon: Icons.photo_library,
              onPressed: _pickImageFromGallery,
              isLoading: _isProcessing,
            ),
          ],
        ),
      );
    }

    if (!_cameraService.isInitialized || _cameraService.controller == null) {
      // Camera initialization failed
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(Icons.error_outline, size: 64, color: Colors.red),
            const SizedBox(height: 16),
            const Text(
              'Failed to initialize camera. Please check your device settings.',
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 16),
            if (_cameraService.lastError != null)
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: Colors.grey[200],
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Text(
                  'Error: ${_cameraService.lastError}',
                  style: Theme.of(
                    context,
                  ).textTheme.bodySmall?.copyWith(color: Colors.red[700]),
                ),
              ),
            const SizedBox(height: 24),
            AppButton(
              label: 'Try Again',
              icon: Icons.refresh,
              onPressed: () {
                setState(() {
                  _errorMessage = '';
                  _initializeCamera();
                });
              },
            ),
            const SizedBox(height: 16),
            AppButton(
              label: 'Use Gallery Instead',
              icon: Icons.photo_library,
              onPressed: _pickImageFromGallery,
              variant: ButtonVariant.outline,
            ),
          ],
        ),
      );
    }

    // Normal camera preview with Cambodia ID card overlay
    return Stack(
      children: [
        // Camera preview
        SizedBox(
          width: double.infinity,
          height: double.infinity,
          child: CameraPreview(_cameraService.controller!),
        ),

        // ID card overlay
        CambodiaIDCardOverlay(
          isFrontSide: _isFrontSide,
          guideText:
              _isFrontSide
                  ? 'Position Cambodia ID card front side within the frame'
                  : 'Position Cambodia ID card back side within the frame',
        ),

        // Bottom controls
        Align(
          alignment: Alignment.bottomCenter,
          child: Padding(
            padding: const EdgeInsets.all(16.0),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                AppButton(
                  label: 'Gallery',
                  icon: Icons.photo_library,
                  onPressed: _pickImageFromGallery,
                  variant: ButtonVariant.outline,
                ),
                AppButton(
                  label: 'Capture',
                  icon: Icons.camera_alt,
                  onPressed: _takePicture,
                  isLoading: _isProcessing,
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildResultView() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          // Display captured image
          Container(
            height: 250,
            decoration: BoxDecoration(
              border: Border.all(
                color: Theme.of(context).colorScheme.primary,
                width: 2,
              ),
              borderRadius: BorderRadius.circular(8),
            ),
            child: ClipRRect(
              borderRadius: BorderRadius.circular(6),
              child: Image.file(_capturedImage!, fit: BoxFit.contain),
            ),
          ),

          const SizedBox(height: 20),

          // Display extracted data
          if (_isProcessing)
            const Center(child: CircularProgressIndicator())
          else if (_extractedData != null)
            _buildExtractedDataCard()
          else
            const Text(
              'No data could be extracted from the image. Try again with a clearer image.',
              style: TextStyle(color: Colors.red),
              textAlign: TextAlign.center,
            ),

          const SizedBox(height: 20),

          // Action buttons
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            children: [
              AppButton(
                label: 'Try Again',
                icon: Icons.refresh,
                onPressed: _resetState,
                variant: ButtonVariant.outline,
              ),
              if (_extractedData != null)
                AppButton(
                  label: 'Continue',
                  icon: Icons.arrow_forward,
                  onPressed: _proceedToLoanForm,
                ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildExtractedDataCard() {
    return Card(
      elevation: 4,
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Extracted Information',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const Divider(),
            _buildDataRow('ID Number', _extractedData!.idNumber),
            _buildDataRow('Full Name', _extractedData!.fullName),
            _buildDataRow('Date of Birth', _extractedData!.dateOfBirth),
            _buildDataRow('Address', _extractedData!.address),
            if (_extractedData!.gender != null)
              _buildDataRow('Gender', _extractedData!.gender!),
            if (_extractedData!.nationality != null)
              _buildDataRow('Nationality', _extractedData!.nationality!),
            if (_extractedData!.expiryDate != null)
              _buildDataRow('Expiry Date', _extractedData!.expiryDate!),
            if (_extractedData!.issueDate != null)
              _buildDataRow('Issue Date', _extractedData!.issueDate!),
          ],
        ),
      ),
    );
  }

  Widget _buildDataRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4.0),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 100,
            child: Text(
              '$label:',
              style: const TextStyle(fontWeight: FontWeight.bold),
            ),
          ),
          Expanded(
            child: Text(
              value.isEmpty ? 'Not detected' : value,
              style: TextStyle(
                color: value.isEmpty ? Colors.red : null,
                fontStyle: value.isEmpty ? FontStyle.italic : null,
              ),
            ),
          ),
        ],
      ),
    );
  }
}
