import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter_native_splash/flutter_native_splash.dart';
import 'package:flutter_spinkit/flutter_spinkit.dart';
import 'package:provider/provider.dart';
import 'screens/login_screen.dart';
import 'screens/register_screen.dart';
import 'screens/home_screen.dart';
import 'screens/theme_settings_screen.dart';
import 'screens/ocr_screen.dart';
import 'screens/cambodia_id_card_scan_screen.dart';
import 'screens/khmer_ocr_screen.dart';
import 'services/auth_service.dart';
import 'models/user_model.dart';
import 'theme/app_theme.dart';
import 'providers/theme_provider.dart';

void main() async {
  // Ensure Flutter is initialized and preserve the native splash screen
  WidgetsBinding widgetsBinding = WidgetsFlutterBinding.ensureInitialized();
  FlutterNativeSplash.preserve(widgetsBinding: widgetsBinding);

  // Log Flutter and Dart versions for debugging
  debugPrint('Flutter version: ${WidgetsBinding.instance.runtimeType}');
  debugPrint('Dart SDK version: ${Platform.version}');
  debugPrint(
    'Platform: ${Platform.operatingSystem} ${Platform.operatingSystemVersion}',
  );

  // Initialize theme provider
  final themeProvider = ThemeProvider();
  await themeProvider.initialize();

  // Run the app
  runApp(
    ChangeNotifierProvider.value(value: themeProvider, child: const MyApp()),
  );
}

class MyApp extends StatefulWidget {
  const MyApp({super.key});

  @override
  State<MyApp> createState() => _MyAppState();
}

class _MyAppState extends State<MyApp> {
  final AuthService _authService = AuthService();
  bool _isInitializing = true;
  UserModel? _currentUser;

  @override
  void initState() {
    super.initState();
    _initializeApp();
  }

  // Initialize the app and check authentication status
  Future<void> _initializeApp() async {
    try {
      // Check if user is already logged in with a valid token
      final user = await _authService.getSavedUser();

      setState(() {
        _currentUser = user;
        _isInitializing = false;
      });
    } catch (e) {
      // Log error using debugPrint which is stripped in release mode
      debugPrint('Error initializing app: $e');
      setState(() {
        _currentUser = null;
        _isInitializing = false;
      });
    } finally {
      // Remove the native splash screen once our app is ready
      FlutterNativeSplash.remove();
    }
  }

  // This widget is the root of your application.
  @override
  Widget build(BuildContext context) {
    final themeProvider = Provider.of<ThemeProvider>(context);

    return MaterialApp(
      title: 'LC Mobile App',
      theme: AppTheme.lightTheme,
      darkTheme: AppTheme.darkTheme,
      themeMode: themeProvider.themeMode, // Use theme provider
      home:
          _isInitializing
              ? const SplashScreen()
              : _currentUser != null
              ? const HomeScreen()
              : const LoginScreen(),
      routes: {
        '/login': (context) => const LoginScreen(),
        '/register': (context) => const RegisterScreen(),
        '/home': (context) => const HomeScreen(),
        '/theme_settings': (context) => const ThemeSettingsScreen(),
        '/ocr': (context) => const OcrScreen(),
        '/cambodia_id_scan': (context) => const CambodiaIDCardScanScreen(),
        '/khmer_ocr': (context) => const KhmerOcrScreen(),
      },
      // Enable navigation debugging
      navigatorObservers: [_NavigatorObserver()],
      debugShowCheckedModeBanner: false, // Remove debug banner
    );
  }
}

// Splash screen widget shown during app initialization
class SplashScreen extends StatelessWidget {
  const SplashScreen({super.key});

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Scaffold(
      backgroundColor: theme.colorScheme.primary,
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            // App logo
            Image.asset('assets/images/logo.png', width: 150, height: 150),
            const SizedBox(height: 30),
            // Loading indicator
            SpinKitThreeBounce(color: theme.colorScheme.onPrimary, size: 30.0),
          ],
        ),
      ),
    );
  }
}

// Custom navigator observer for debugging
class _NavigatorObserver extends NavigatorObserver {
  // Log navigation events
  void _log(String message) {
    // In a production app, you would use a proper logging framework
    // For now, we'll keep the print statements but wrap them in a debug flag check
    assert(() {
      debugPrint(message);
      return true;
    }());
  }

  @override
  void didPush(Route<dynamic> route, Route<dynamic>? previousRoute) {
    _log(
      'Navigation: Pushed ${route.settings.name} (from ${previousRoute?.settings.name})',
    );
  }

  @override
  void didPop(Route<dynamic> route, Route<dynamic>? previousRoute) {
    _log(
      'Navigation: Popped ${route.settings.name} (back to ${previousRoute?.settings.name})',
    );
  }

  @override
  void didReplace({Route<dynamic>? newRoute, Route<dynamic>? oldRoute}) {
    _log(
      'Navigation: Replaced ${oldRoute?.settings.name} with ${newRoute?.settings.name}',
    );
  }

  @override
  void didRemove(Route<dynamic> route, Route<dynamic>? previousRoute) {
    _log('Navigation: Removed ${route.settings.name}');
  }
}
