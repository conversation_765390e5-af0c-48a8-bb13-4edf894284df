{"logs": [{"outputFile": "com.example.lc_mobile_app-mergeDebugResources-52:/values/values.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\5093ab42d2307deb2d7ac0b7f5718c38\\transformed\\jetified-window-1.2.0\\res\\values\\values.xml", "from": {"startLines": "2,3,9,17,25,37,43,49,50,51,52,53,54,55,61,66,74,89", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,114,287,506,725,1039,1227,1414,1467,1527,1579,1624,1663,1723,1918,2076,2358,2972", "endLines": "2,8,16,24,36,42,48,49,50,51,52,53,54,60,65,73,88,104", "endColumns": "58,11,11,11,11,11,11,52,59,51,44,38,59,24,24,24,24,24", "endOffsets": "109,282,501,720,1034,1222,1409,1462,1522,1574,1619,1658,1718,1913,2071,2353,2967,3621"}, "to": {"startLines": "2,5,11,19,42,54,60,66,67,68,69,70,324,2249,2255,3614,3622,3637", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,280,453,672,1504,1818,2006,2193,2246,2306,2358,2403,19444,144280,144475,191438,191720,192334", "endLines": "2,10,18,26,53,59,65,66,67,68,69,70,324,2254,2259,3621,3636,3652", "endColumns": "58,11,11,11,11,11,11,52,59,51,44,38,59,24,24,24,24,24", "endOffsets": "159,448,667,886,1813,2001,2188,2241,2301,2353,2398,2437,19499,144470,144628,191715,192329,192983"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\45af1ebc35cbf9d2d2886a132166b73a\\transformed\\transition-1.4.1\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11", "startColumns": "4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,95,142,185,240,287,341,393,442,503", "endColumns": "39,46,42,54,46,53,51,48,60,49", "endOffsets": "90,137,180,235,282,336,388,437,498,548"}, "to": {"startLines": "326,327,333,340,341,360,361,362,363,364", "startColumns": "4,4,4,4,4,4,4,4,4,4", "startOffsets": "19561,19601,19880,20218,20273,21290,21344,21396,21445,21506", "endColumns": "39,46,42,54,46,53,51,48,60,49", "endOffsets": "19596,19643,19918,20268,20315,21339,21391,21440,21501,21551"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\75e1bcd7a8b61b1e132d50e7766bfd37\\transformed\\recyclerview-1.0.0\\res\\values\\values.xml", "from": {"startLines": "30,31,32,33,34,35,36,2", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "1535,1594,1642,1698,1773,1849,1921,55", "endLines": "30,31,32,33,34,35,36,29", "endColumns": "58,47,55,74,75,71,65,24", "endOffsets": "1589,1637,1693,1768,1844,1916,1982,1530"}, "to": {"startLines": "242,243,244,252,253,254,329,3515", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "14255,14314,14362,15029,15104,15180,19682,188379", "endLines": "242,243,244,252,253,254,329,3534", "endColumns": "58,47,55,74,75,71,65,24", "endOffsets": "14309,14357,14413,15099,15175,15247,19743,189169"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\58a0920e123e93dd6aa702d27ab7530e\\transformed\\coordinatorlayout-1.0.0\\res\\values\\values.xml", "from": {"startLines": "2,102,3,13", "startColumns": "4,4,4,4", "startOffsets": "55,5935,116,724", "endLines": "2,104,12,101", "endColumns": "60,12,24,24", "endOffsets": "111,6075,719,5930"}, "to": {"startLines": "3,2142,2889,2895", "startColumns": "4,4,4,4", "startOffsets": "164,140612,166056,166267", "endLines": "3,2144,2894,2978", "endColumns": "60,12,24,24", "endOffsets": "220,140752,166262,170778"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\b3d51a44ab6b56289d4858158a1ad6dd\\transformed\\jetified-savedstate-1.2.1\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "53", "endOffsets": "104"}, "to": {"startLines": "368", "startColumns": "4", "startOffsets": "21691", "endColumns": "53", "endOffsets": "21740"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\a12a090674da1baef97900c09c2e9654\\transformed\\jetified-appcompat-resources-1.6.1\\res\\values\\values.xml", "from": {"startLines": "2,18,24,34,50", "startColumns": "4,4,4,4,4", "startOffsets": "55,480,658,942,1353", "endLines": "17,23,33,49,53", "endColumns": "24,24,24,24,24", "endOffsets": "475,653,937,1348,1475"}, "to": {"startLines": "2270,2286,2292,3653,3669", "startColumns": "4,4,4,4,4", "startOffsets": "145150,145575,145753,192988,193399", "endLines": "2285,2291,2301,3668,3672", "endColumns": "24,24,24,24,24", "endOffsets": "145570,145748,146032,193394,193521"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\c092edbccc16347970ed4f22e8da111a\\transformed\\lifecycle-runtime-2.7.0\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "42", "endOffsets": "93"}, "to": {"startLines": "366", "startColumns": "4", "startOffsets": "21588", "endColumns": "42", "endOffsets": "21626"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\67e431d42222568371fff0afd22bbdc6\\transformed\\appcompat-1.6.1\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,54,55,56,57,58,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,221,222,226,230,234,239,245,252,256,260,265,269,273,277,281,285,289,295,299,305,309,315,319,324,328,331,335,341,345,351,355,361,364,368,372,376,380,384,385,386,387,390,393,396,399,403,404,405,406,407,410,412,414,416,421,422,426,432,436,437,439,451,452,456,462,466,467,468,472,499,503,504,508,536,708,734,905,931,962,970,976,992,1014,1019,1024,1034,1043,1052,1056,1063,1082,1089,1090,1099,1102,1105,1109,1113,1117,1120,1121,1126,1131,1141,1146,1153,1159,1160,1163,1167,1172,1174,1176,1179,1182,1184,1188,1191,1198,1201,1204,1208,1210,1214,1216,1218,1220,1224,1232,1240,1252,1258,1267,1270,1281,1284,1285,1290,1291,1296,1365,1435,1436,1446,1455,1456,1458,1462,1465,1468,1471,1474,1477,1480,1483,1487,1490,1493,1496,1500,1503,1507,1511,1512,1513,1514,1515,1516,1517,1518,1519,1520,1521,1522,1523,1524,1525,1526,1527,1528,1529,1530,1531,1533,1535,1536,1537,1538,1539,1540,1541,1542,1544,1545,1547,1548,1550,1552,1553,1555,1556,1557,1558,1559,1560,1562,1563,1564,1565,1566,1567,1569,1571,1573,1574,1575,1576,1577,1578,1579,1580,1581,1582,1583,1584,1585,1587,1588,1589,1590,1591,1592,1593,1595,1599,1603,1604,1605,1606,1607,1608,1612,1613,1614,1615,1617,1619,1621,1623,1625,1626,1627,1628,1630,1632,1634,1635,1636,1637,1638,1639,1640,1641,1642,1643,1644,1645,1648,1649,1650,1651,1653,1655,1656,1658,1659,1661,1663,1665,1666,1667,1668,1669,1670,1671,1672,1673,1674,1675,1676,1678,1679,1680,1681,1683,1684,1685,1686,1687,1689,1691,1693,1695,1696,1697,1698,1699,1700,1701,1702,1703,1704,1705,1706,1707,1708,1709,1710,1785,1788,1791,1794,1808,1814,1824,1827,1856,1883,1892,1956,2319,2323,2351,2379,2397,2421,2427,2433,2454,2578,2598,2604,2608,2614,2649,2661,2727,2747,2802,2814,2840", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,160,205,254,295,350,412,476,546,607,682,758,835,913,998,1080,1156,1232,1309,1387,1493,1599,1678,1758,1815,1873,1947,2022,2087,2153,2213,2274,2346,2419,2486,2554,2613,2672,2731,2790,2849,2903,2957,3010,3064,3118,3172,3226,3300,3379,3452,3597,3669,3741,3814,3871,4002,4076,4150,4225,4297,4370,4440,4511,4571,4632,4701,4770,4840,4914,4990,5054,5131,5207,5284,5349,5418,5495,5570,5639,5707,5784,5850,5911,6008,6073,6142,6241,6312,6371,6429,6486,6545,6609,6680,6752,6824,6896,6968,7035,7103,7171,7230,7293,7357,7447,7538,7598,7664,7731,7797,7867,7931,7984,8051,8112,8179,8292,8350,8413,8478,8543,8618,8691,8763,8807,8854,8900,8949,9010,9071,9132,9194,9258,9322,9386,9451,9514,9574,9635,9701,9760,9820,9882,9953,10013,10081,10167,10254,10344,10431,10519,10601,10684,10774,10865,10917,10975,11020,11086,11150,11207,11264,11318,11375,11423,11472,11523,11557,11604,11653,11699,11731,11795,11917,11974,12048,12118,12196,12250,12320,12405,12453,12499,12560,12623,12689,12753,12824,12887,12952,13016,13077,13138,13190,13263,13337,13406,13481,13555,13629,13770,13840,13893,13971,14061,14149,14245,14335,14917,15006,15253,15534,15786,16071,16464,16941,17163,17385,17661,17888,18118,18348,18578,18808,19035,19454,19680,20105,20335,20763,20982,21265,21473,21604,21831,22257,22482,22909,23130,23555,23675,23951,24252,24576,24867,25181,25318,25449,25554,25796,25963,26167,26375,26646,26758,26870,26975,27092,27306,27452,27592,27678,28026,28114,28360,28778,29027,29109,29207,29864,29964,30216,30640,30895,30989,31078,31315,33339,33581,33683,33936,36092,46773,48289,58984,60512,62269,62895,63315,64576,65841,66097,66333,66880,67374,67979,68177,68757,70125,70500,70618,71156,71313,71509,71782,72038,72208,72349,72413,72778,73145,73821,74085,74423,74776,74870,75056,75362,75624,75749,75876,76115,76326,76445,76638,76815,77270,77451,77573,77832,77945,78132,78234,78341,78470,78745,79253,79749,80626,80920,81490,81639,82371,82543,82627,82963,83055,83333,88564,93935,93997,94575,95159,95250,95363,95592,95752,95904,96075,96241,96410,96577,96740,96983,97153,97326,97497,97771,97970,98175,98505,98589,98685,98781,98879,98979,99081,99183,99285,99387,99489,99589,99685,99797,99926,100049,100180,100311,100409,100523,100617,100757,100891,100987,101099,101199,101315,101411,101523,101623,101763,101899,102063,102193,102351,102501,102642,102786,102921,103033,103183,103311,103439,103575,103707,103837,103967,104079,104219,104365,104509,104647,104713,104803,104879,104983,105073,105175,105283,105391,105491,105571,105663,105761,105871,105923,106001,106107,106199,106303,106413,106535,106698,106855,106935,107035,107125,107235,107325,107566,107660,107766,107858,107958,108070,108184,108300,108416,108510,108624,108736,108838,108958,109080,109162,109266,109386,109512,109610,109704,109792,109904,110020,110142,110254,110429,110545,110631,110723,110835,110959,111026,111152,111220,111348,111492,111620,111689,111784,111899,112012,112111,112220,112331,112442,112543,112648,112748,112878,112969,113092,113186,113298,113384,113488,113584,113672,113790,113894,113998,114124,114212,114320,114420,114510,114620,114704,114806,114890,114944,115008,115114,115200,115310,115394,115514,118130,118248,118363,118443,118804,119037,119554,119632,120976,122337,122725,125568,135621,135756,137126,138483,139055,139806,140068,140268,140647,144925,145531,145760,145911,146126,147209,147521,150547,151291,153422,153762,155073", "endLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,54,55,56,57,58,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,220,221,225,229,233,238,244,251,255,259,264,268,272,276,280,284,288,294,298,304,308,314,318,323,327,330,334,340,344,350,354,360,363,367,371,375,379,383,384,385,386,389,392,395,398,402,403,404,405,406,409,411,413,415,420,421,425,431,435,436,438,450,451,455,461,465,466,467,471,498,502,503,507,535,707,733,904,930,961,969,975,991,1013,1018,1023,1033,1042,1051,1055,1062,1081,1088,1089,1098,1101,1104,1108,1112,1116,1119,1120,1125,1130,1140,1145,1152,1158,1159,1162,1166,1171,1173,1175,1178,1181,1183,1187,1190,1197,1200,1203,1207,1209,1213,1215,1217,1219,1223,1231,1239,1251,1257,1266,1269,1280,1283,1284,1289,1290,1295,1364,1434,1435,1445,1454,1455,1457,1461,1464,1467,1470,1473,1476,1479,1482,1486,1489,1492,1495,1499,1502,1506,1510,1511,1512,1513,1514,1515,1516,1517,1518,1519,1520,1521,1522,1523,1524,1525,1526,1527,1528,1529,1530,1532,1534,1535,1536,1537,1538,1539,1540,1541,1543,1544,1546,1547,1549,1551,1552,1554,1555,1556,1557,1558,1559,1561,1562,1563,1564,1565,1566,1568,1570,1572,1573,1574,1575,1576,1577,1578,1579,1580,1581,1582,1583,1584,1586,1587,1588,1589,1590,1591,1592,1594,1598,1602,1603,1604,1605,1606,1607,1611,1612,1613,1614,1616,1618,1620,1622,1624,1625,1626,1627,1629,1631,1633,1634,1635,1636,1637,1638,1639,1640,1641,1642,1643,1644,1647,1648,1649,1650,1652,1654,1655,1657,1658,1660,1662,1664,1665,1666,1667,1668,1669,1670,1671,1672,1673,1674,1675,1677,1678,1679,1680,1682,1683,1684,1685,1686,1688,1690,1692,1694,1695,1696,1697,1698,1699,1700,1701,1702,1703,1704,1705,1706,1707,1708,1709,1784,1787,1790,1793,1807,1813,1823,1826,1855,1882,1891,1955,2318,2322,2350,2378,2396,2420,2426,2432,2453,2577,2597,2603,2607,2613,2648,2660,2726,2746,2801,2813,2839,2846", "endColumns": "54,44,48,40,54,61,63,69,60,74,75,76,77,84,81,75,75,76,77,105,105,78,79,56,57,73,74,64,65,59,60,71,72,66,67,58,58,58,58,58,53,53,52,53,53,53,53,73,78,72,73,71,71,72,56,57,73,73,74,71,72,69,70,59,60,68,68,69,73,75,63,76,75,76,64,68,76,74,68,67,76,65,60,96,64,68,98,70,58,57,56,58,63,70,71,71,71,71,66,67,67,58,62,63,89,90,59,65,66,65,69,63,52,66,60,66,112,57,62,64,64,74,72,71,43,46,45,48,60,60,60,61,63,63,63,64,62,59,60,65,58,59,61,70,59,67,85,86,89,86,87,81,82,89,90,51,57,44,65,63,56,56,53,56,47,48,50,33,46,48,45,31,63,61,56,73,69,77,53,69,84,47,45,60,62,65,63,70,62,64,63,60,60,51,72,73,68,74,73,73,140,69,52,77,89,87,95,89,12,88,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,136,130,104,12,12,12,12,12,111,111,104,116,12,12,12,12,12,87,12,12,12,81,12,12,99,12,12,12,93,88,12,12,12,101,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,117,12,12,12,12,12,12,12,63,12,12,12,12,12,12,93,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,83,12,91,12,12,12,61,12,12,90,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,83,95,95,97,99,101,101,101,101,101,99,95,111,128,122,130,130,97,113,93,12,12,95,111,99,115,95,111,99,12,135,12,129,12,12,140,12,134,111,149,127,127,12,131,129,129,111,139,12,12,12,65,89,75,103,89,101,107,107,99,79,91,97,12,51,77,105,91,103,109,12,12,12,79,99,89,109,89,12,93,105,91,12,12,12,12,12,93,113,111,12,12,12,81,103,119,125,97,93,87,111,115,121,111,12,115,85,91,12,12,66,12,67,12,12,12,68,94,114,112,98,108,110,110,100,104,99,12,90,122,93,12,85,103,95,87,12,12,12,12,87,107,99,89,109,83,101,83,53,63,105,85,109,83,119,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24", "endOffsets": "155,200,249,290,345,407,471,541,602,677,753,830,908,993,1075,1151,1227,1304,1382,1488,1594,1673,1753,1810,1868,1942,2017,2082,2148,2208,2269,2341,2414,2481,2549,2608,2667,2726,2785,2844,2898,2952,3005,3059,3113,3167,3221,3295,3374,3447,3521,3664,3736,3809,3866,3924,4071,4145,4220,4292,4365,4435,4506,4566,4627,4696,4765,4835,4909,4985,5049,5126,5202,5279,5344,5413,5490,5565,5634,5702,5779,5845,5906,6003,6068,6137,6236,6307,6366,6424,6481,6540,6604,6675,6747,6819,6891,6963,7030,7098,7166,7225,7288,7352,7442,7533,7593,7659,7726,7792,7862,7926,7979,8046,8107,8174,8287,8345,8408,8473,8538,8613,8686,8758,8802,8849,8895,8944,9005,9066,9127,9189,9253,9317,9381,9446,9509,9569,9630,9696,9755,9815,9877,9948,10008,10076,10162,10249,10339,10426,10514,10596,10679,10769,10860,10912,10970,11015,11081,11145,11202,11259,11313,11370,11418,11467,11518,11552,11599,11648,11694,11726,11790,11852,11969,12043,12113,12191,12245,12315,12400,12448,12494,12555,12618,12684,12748,12819,12882,12947,13011,13072,13133,13185,13258,13332,13401,13476,13550,13624,13765,13835,13888,13966,14056,14144,14240,14330,14912,15001,15248,15529,15781,16066,16459,16936,17158,17380,17656,17883,18113,18343,18573,18803,19030,19449,19675,20100,20330,20758,20977,21260,21468,21599,21826,22252,22477,22904,23125,23550,23670,23946,24247,24571,24862,25176,25313,25444,25549,25791,25958,26162,26370,26641,26753,26865,26970,27087,27301,27447,27587,27673,28021,28109,28355,28773,29022,29104,29202,29859,29959,30211,30635,30890,30984,31073,31310,33334,33576,33678,33931,36087,46768,48284,58979,60507,62264,62890,63310,64571,65836,66092,66328,66875,67369,67974,68172,68752,70120,70495,70613,71151,71308,71504,71777,72033,72203,72344,72408,72773,73140,73816,74080,74418,74771,74865,75051,75357,75619,75744,75871,76110,76321,76440,76633,76810,77265,77446,77568,77827,77940,78127,78229,78336,78465,78740,79248,79744,80621,80915,81485,81634,82366,82538,82622,82958,83050,83328,88559,93930,93992,94570,95154,95245,95358,95587,95747,95899,96070,96236,96405,96572,96735,96978,97148,97321,97492,97766,97965,98170,98500,98584,98680,98776,98874,98974,99076,99178,99280,99382,99484,99584,99680,99792,99921,100044,100175,100306,100404,100518,100612,100752,100886,100982,101094,101194,101310,101406,101518,101618,101758,101894,102058,102188,102346,102496,102637,102781,102916,103028,103178,103306,103434,103570,103702,103832,103962,104074,104214,104360,104504,104642,104708,104798,104874,104978,105068,105170,105278,105386,105486,105566,105658,105756,105866,105918,105996,106102,106194,106298,106408,106530,106693,106850,106930,107030,107120,107230,107320,107561,107655,107761,107853,107953,108065,108179,108295,108411,108505,108619,108731,108833,108953,109075,109157,109261,109381,109507,109605,109699,109787,109899,110015,110137,110249,110424,110540,110626,110718,110830,110954,111021,111147,111215,111343,111487,111615,111684,111779,111894,112007,112106,112215,112326,112437,112538,112643,112743,112873,112964,113087,113181,113293,113379,113483,113579,113667,113785,113889,113993,114119,114207,114315,114415,114505,114615,114699,114801,114885,114939,115003,115109,115195,115305,115389,115509,118125,118243,118358,118438,118799,119032,119549,119627,120971,122332,122720,125563,135616,135751,137121,138478,139050,139801,140063,140263,140642,144920,145526,145755,145906,146121,147204,147516,150542,151286,153417,153757,155068,155271"}, "to": {"startLines": "4,27,32,71,72,73,75,76,77,78,79,80,81,84,85,86,87,88,89,90,91,92,93,94,95,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,132,133,134,135,137,138,139,140,141,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,240,241,245,246,247,248,249,250,251,277,278,279,280,281,282,283,284,320,321,322,323,328,337,338,343,365,371,372,374,379,380,381,382,383,384,385,386,387,388,389,390,391,392,393,394,395,396,397,398,399,400,401,402,403,404,405,438,443,444,445,446,447,448,456,457,461,465,469,474,480,487,491,495,500,504,508,512,516,520,524,530,534,540,544,550,554,559,563,566,570,576,580,586,590,596,599,603,607,611,615,619,620,621,622,625,628,631,634,638,639,640,641,642,645,647,649,651,656,657,661,667,671,672,674,686,687,691,697,701,702,703,707,734,738,739,743,771,943,969,1140,1166,1197,1205,1211,1227,1249,1254,1259,1269,1278,1287,1291,1298,1317,1324,1325,1334,1337,1340,1344,1348,1352,1355,1356,1361,1366,1376,1381,1388,1394,1395,1398,1402,1407,1409,1411,1414,1417,1419,1423,1426,1433,1436,1439,1443,1445,1449,1451,1453,1455,1459,1467,1475,1487,1493,1502,1505,1516,1519,1520,1525,1526,1559,1628,1698,1699,1709,1718,1870,1872,1876,1879,1882,1885,1888,1891,1894,1897,1901,1904,1907,1910,1914,1917,1921,1925,1926,1927,1928,1929,1930,1931,1932,1933,1934,1935,1936,1937,1938,1939,1940,1941,1942,1943,1944,1945,1947,1949,1950,1951,1952,1953,1954,1955,1956,1958,1959,1961,1962,1964,1966,1967,1969,1970,1971,1972,1973,1974,1976,1977,1978,1979,1980,1997,1999,2001,2003,2004,2005,2006,2007,2008,2009,2010,2011,2012,2013,2014,2015,2017,2018,2019,2020,2021,2022,2023,2025,2029,2033,2034,2035,2036,2037,2038,2042,2043,2044,2045,2047,2049,2051,2053,2055,2056,2057,2058,2060,2062,2064,2065,2066,2067,2068,2069,2070,2071,2072,2073,2074,2075,2078,2079,2080,2081,2083,2085,2086,2088,2089,2091,2093,2095,2096,2097,2098,2099,2100,2101,2102,2103,2104,2105,2106,2108,2109,2110,2111,2113,2114,2115,2116,2117,2119,2121,2123,2125,2126,2127,2128,2129,2130,2131,2132,2133,2134,2135,2136,2137,2138,2139,2145,2220,2223,2226,2229,2243,2260,2302,2305,2334,2361,2370,2434,2802,2823,2861,2999,3123,3147,3153,3182,3203,3327,3355,3361,3509,3535,3602,3673,3773,3793,3848,3860,3886", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "225,891,1088,2442,2483,2538,2673,2737,2807,2868,2943,3019,3096,3334,3419,3501,3577,3653,3730,3808,3914,4020,4099,4179,4236,5096,5170,5245,5310,5376,5436,5497,5569,5642,5709,5777,5836,5895,5954,6013,6072,6126,6180,6233,6287,6341,6395,6739,6813,6892,6965,7110,7182,7254,7327,7384,7515,7589,7663,7738,7810,7883,7953,8024,8084,8145,8214,8283,8353,8427,8503,8567,8644,8720,8797,8862,8931,9008,9083,9152,9220,9297,9363,9424,9521,9586,9655,9754,9825,9884,9942,9999,10058,10122,10193,10265,10337,10409,10481,10548,10616,10684,10743,10806,10870,10960,11051,11111,11177,11244,11310,11380,11444,11497,11564,11625,11692,11805,11863,11926,11991,12056,12131,12204,12276,12320,12367,12413,12462,12523,12584,12645,12707,12771,12835,12899,12964,13027,13087,13148,13214,13273,13333,13395,13466,13526,14082,14168,14418,14508,14595,14683,14765,14848,14938,16663,16715,16773,16818,16884,16948,17005,17062,19239,19296,19344,19393,19648,20080,20127,20385,21556,21859,21923,22045,22366,22440,22510,22588,22642,22712,22797,22845,22891,22952,23015,23081,23145,23216,23279,23344,23408,23469,23530,23582,23655,23729,23798,23873,23947,24021,24162,27438,27799,27877,27967,28055,28151,28241,28823,28912,29159,29440,29692,29977,30370,30847,31069,31291,31567,31794,32024,32254,32484,32714,32941,33360,33586,34011,34241,34669,34888,35171,35379,35510,35737,36163,36388,36815,37036,37461,37581,37857,38158,38482,38773,39087,39224,39355,39460,39702,39869,40073,40281,40552,40664,40776,40881,40998,41212,41358,41498,41584,41932,42020,42266,42684,42933,43015,43113,43770,43870,44122,44546,44801,44895,44984,45221,47245,47487,47589,47842,49998,60679,62195,72890,74418,76175,76801,77221,78482,79747,80003,80239,80786,81280,81885,82083,82663,84031,84406,84524,85062,85219,85415,85688,85944,86114,86255,86319,86684,87051,87727,87991,88329,88682,88776,88962,89268,89530,89655,89782,90021,90232,90351,90544,90721,91176,91357,91479,91738,91851,92038,92140,92247,92376,92651,93159,93655,94532,94826,95396,95545,96277,96449,96533,96869,96961,99296,104527,109898,109960,110538,111122,119069,119182,119411,119571,119723,119894,120060,120229,120396,120559,120802,120972,121145,121316,121590,121789,121994,122324,122408,122504,122600,122698,122798,122900,123002,123104,123206,123308,123408,123504,123616,123745,123868,123999,124130,124228,124342,124436,124576,124710,124806,124918,125018,125134,125230,125342,125442,125582,125718,125882,126012,126170,126320,126461,126605,126740,126852,127002,127130,127258,127394,127526,127656,127786,127898,129178,129324,129468,129606,129672,129762,129838,129942,130032,130134,130242,130350,130450,130530,130622,130720,130830,130882,130960,131066,131158,131262,131372,131494,131657,131814,131894,131994,132084,132194,132284,132525,132619,132725,132817,132917,133029,133143,133259,133375,133469,133583,133695,133797,133917,134039,134121,134225,134345,134471,134569,134663,134751,134863,134979,135101,135213,135388,135504,135590,135682,135794,135918,135985,136111,136179,136307,136451,136579,136648,136743,136858,136971,137070,137179,137290,137401,137502,137607,137707,137837,137928,138051,138145,138257,138343,138447,138543,138631,138749,138853,138957,139083,139171,139279,139379,139469,139579,139663,139765,139849,139903,139967,140073,140159,140269,140353,140757,143373,143491,143606,143686,144047,144633,146037,146115,147459,148820,149208,152051,162289,163028,164699,171512,175813,176564,176826,177673,178052,182330,183184,183413,188164,189174,191126,193526,197650,198394,200525,200865,202176", "endLines": "4,27,32,71,72,73,75,76,77,78,79,80,81,84,85,86,87,88,89,90,91,92,93,94,95,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,132,133,134,135,137,138,139,140,141,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,240,241,245,246,247,248,249,250,251,277,278,279,280,281,282,283,284,320,321,322,323,328,337,338,343,365,371,372,374,379,380,381,382,383,384,385,386,387,388,389,390,391,392,393,394,395,396,397,398,399,400,401,402,403,404,405,438,443,444,445,446,447,455,456,460,464,468,473,479,486,490,494,499,503,507,511,515,519,523,529,533,539,543,549,553,558,562,565,569,575,579,585,589,595,598,602,606,610,614,618,619,620,621,624,627,630,633,637,638,639,640,641,644,646,648,650,655,656,660,666,670,671,673,685,686,690,696,700,701,702,706,733,737,738,742,770,942,968,1139,1165,1196,1204,1210,1226,1248,1253,1258,1268,1277,1286,1290,1297,1316,1323,1324,1333,1336,1339,1343,1347,1351,1354,1355,1360,1365,1375,1380,1387,1393,1394,1397,1401,1406,1408,1410,1413,1416,1418,1422,1425,1432,1435,1438,1442,1444,1448,1450,1452,1454,1458,1466,1474,1486,1492,1501,1504,1515,1518,1519,1524,1525,1530,1627,1697,1698,1708,1717,1718,1871,1875,1878,1881,1884,1887,1890,1893,1896,1900,1903,1906,1909,1913,1916,1920,1924,1925,1926,1927,1928,1929,1930,1931,1932,1933,1934,1935,1936,1937,1938,1939,1940,1941,1942,1943,1944,1946,1948,1949,1950,1951,1952,1953,1954,1955,1957,1958,1960,1961,1963,1965,1966,1968,1969,1970,1971,1972,1973,1975,1976,1977,1978,1979,1980,1998,2000,2002,2003,2004,2005,2006,2007,2008,2009,2010,2011,2012,2013,2014,2016,2017,2018,2019,2020,2021,2022,2024,2028,2032,2033,2034,2035,2036,2037,2041,2042,2043,2044,2046,2048,2050,2052,2054,2055,2056,2057,2059,2061,2063,2064,2065,2066,2067,2068,2069,2070,2071,2072,2073,2074,2077,2078,2079,2080,2082,2084,2085,2087,2088,2090,2092,2094,2095,2096,2097,2098,2099,2100,2101,2102,2103,2104,2105,2107,2108,2109,2110,2112,2113,2114,2115,2116,2118,2120,2122,2124,2125,2126,2127,2128,2129,2130,2131,2132,2133,2134,2135,2136,2137,2138,2139,2219,2222,2225,2228,2242,2248,2269,2304,2333,2360,2369,2433,2796,2805,2850,2888,3016,3146,3152,3158,3202,3326,3346,3360,3364,3514,3569,3613,3738,3792,3847,3859,3885,3892", "endColumns": "54,44,48,40,54,61,63,69,60,74,75,76,77,84,81,75,75,76,77,105,105,78,79,56,57,73,74,64,65,59,60,71,72,66,67,58,58,58,58,58,53,53,52,53,53,53,53,73,78,72,73,71,71,72,56,57,73,73,74,71,72,69,70,59,60,68,68,69,73,75,63,76,75,76,64,68,76,74,68,67,76,65,60,96,64,68,98,70,58,57,56,58,63,70,71,71,71,71,66,67,67,58,62,63,89,90,59,65,66,65,69,63,52,66,60,66,112,57,62,64,64,74,72,71,43,46,45,48,60,60,60,61,63,63,63,64,62,59,60,65,58,59,61,70,59,67,85,86,89,86,87,81,82,89,90,51,57,44,65,63,56,56,53,56,47,48,50,33,46,48,45,31,63,61,56,73,69,77,53,69,84,47,45,60,62,65,63,70,62,64,63,60,60,51,72,73,68,74,73,73,140,69,52,77,89,87,95,89,12,88,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,136,130,104,12,12,12,12,12,111,111,104,116,12,12,12,12,12,87,12,12,12,81,12,12,99,12,12,12,93,88,12,12,12,101,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,117,12,12,12,12,12,12,12,63,12,12,12,12,12,12,93,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,83,12,91,12,12,12,61,12,12,90,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,83,95,95,97,99,101,101,101,101,101,99,95,111,128,122,130,130,97,113,93,12,12,95,111,99,115,95,111,99,12,135,12,129,12,12,140,12,134,111,149,127,127,12,131,129,129,111,139,12,12,12,65,89,75,103,89,101,107,107,99,79,91,97,12,51,77,105,91,103,109,12,12,12,79,99,89,109,89,12,93,105,91,12,12,12,12,12,93,113,111,12,12,12,81,103,119,125,97,93,87,111,115,121,111,12,115,85,91,12,12,66,12,67,12,12,12,68,94,114,112,98,108,110,110,100,104,99,12,90,122,93,12,85,103,95,87,12,12,12,12,87,107,99,89,109,83,101,83,53,63,105,85,109,83,119,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24", "endOffsets": "275,931,1132,2478,2533,2595,2732,2802,2863,2938,3014,3091,3169,3414,3496,3572,3648,3725,3803,3909,4015,4094,4174,4231,4289,5165,5240,5305,5371,5431,5492,5564,5637,5704,5772,5831,5890,5949,6008,6067,6121,6175,6228,6282,6336,6390,6444,6808,6887,6960,7034,7177,7249,7322,7379,7437,7584,7658,7733,7805,7878,7948,8019,8079,8140,8209,8278,8348,8422,8498,8562,8639,8715,8792,8857,8926,9003,9078,9147,9215,9292,9358,9419,9516,9581,9650,9749,9820,9879,9937,9994,10053,10117,10188,10260,10332,10404,10476,10543,10611,10679,10738,10801,10865,10955,11046,11106,11172,11239,11305,11375,11439,11492,11559,11620,11687,11800,11858,11921,11986,12051,12126,12199,12271,12315,12362,12408,12457,12518,12579,12640,12702,12766,12830,12894,12959,13022,13082,13143,13209,13268,13328,13390,13461,13521,13589,14163,14250,14503,14590,14678,14760,14843,14933,15024,16710,16768,16813,16879,16943,17000,17057,17111,19291,19339,19388,19439,19677,20122,20171,20426,21583,21918,21980,22097,22435,22505,22583,22637,22707,22792,22840,22886,22947,23010,23076,23140,23211,23274,23339,23403,23464,23525,23577,23650,23724,23793,23868,23942,24016,24157,24227,27486,27872,27962,28050,28146,28236,28818,28907,29154,29435,29687,29972,30365,30842,31064,31286,31562,31789,32019,32249,32479,32709,32936,33355,33581,34006,34236,34664,34883,35166,35374,35505,35732,36158,36383,36810,37031,37456,37576,37852,38153,38477,38768,39082,39219,39350,39455,39697,39864,40068,40276,40547,40659,40771,40876,40993,41207,41353,41493,41579,41927,42015,42261,42679,42928,43010,43108,43765,43865,44117,44541,44796,44890,44979,45216,47240,47482,47584,47837,49993,60674,62190,72885,74413,76170,76796,77216,78477,79742,79998,80234,80781,81275,81880,82078,82658,84026,84401,84519,85057,85214,85410,85683,85939,86109,86250,86314,86679,87046,87722,87986,88324,88677,88771,88957,89263,89525,89650,89777,90016,90227,90346,90539,90716,91171,91352,91474,91733,91846,92033,92135,92242,92371,92646,93154,93650,94527,94821,95391,95540,96272,96444,96528,96864,96956,97234,104522,109893,109955,110533,111117,111208,119177,119406,119566,119718,119889,120055,120224,120391,120554,120797,120967,121140,121311,121585,121784,121989,122319,122403,122499,122595,122693,122793,122895,122997,123099,123201,123303,123403,123499,123611,123740,123863,123994,124125,124223,124337,124431,124571,124705,124801,124913,125013,125129,125225,125337,125437,125577,125713,125877,126007,126165,126315,126456,126600,126735,126847,126997,127125,127253,127389,127521,127651,127781,127893,128033,129319,129463,129601,129667,129757,129833,129937,130027,130129,130237,130345,130445,130525,130617,130715,130825,130877,130955,131061,131153,131257,131367,131489,131652,131809,131889,131989,132079,132189,132279,132520,132614,132720,132812,132912,133024,133138,133254,133370,133464,133578,133690,133792,133912,134034,134116,134220,134340,134466,134564,134658,134746,134858,134974,135096,135208,135383,135499,135585,135677,135789,135913,135980,136106,136174,136302,136446,136574,136643,136738,136853,136966,137065,137174,137285,137396,137497,137602,137702,137832,137923,138046,138140,138252,138338,138442,138538,138626,138744,138848,138952,139078,139166,139274,139374,139464,139574,139658,139760,139844,139898,139962,140068,140154,140264,140348,140468,143368,143486,143601,143681,144042,144275,145145,146110,147454,148815,149203,152046,162099,162419,164393,166051,172079,176559,176821,177021,178047,182325,182931,183408,183559,188374,190252,191433,196547,198389,200520,200860,202171,202374"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\55520e4df2220e27f13f0bbb7467d11a\\transformed\\fragment-1.7.1\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,10", "startColumns": "4,4,4,4,4", "startOffsets": "55,112,177,241,411", "endLines": "2,3,4,9,13", "endColumns": "56,64,63,24,24", "endOffsets": "107,172,236,406,555"}, "to": {"startLines": "325,342,370,3075,3080", "startColumns": "4,4,4,4,4", "startOffsets": "19504,20320,21795,174645,174815", "endLines": "325,342,370,3079,3083", "endColumns": "56,64,63,24,24", "endOffsets": "19556,20380,21854,174810,174959"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\a70ddd560199940b45ffc1a1c4db7f79\\transformed\\jetified-activity-1.9.3\\res\\values\\values.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,97", "endColumns": "41,59", "endOffsets": "92,152"}, "to": {"startLines": "339,367", "startColumns": "4,4", "startOffsets": "20176,21631", "endColumns": "41,59", "endOffsets": "20213,21686"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\2fbaf1b88ed46383f13a9a8058612fab\\transformed\\jetified-camera-view-1.4.2\\res\\values\\values.xml", "from": {"startLines": "2,6,14", "startColumns": "4,4,4", "startOffsets": "55,207,514", "endLines": "5,13,17", "endColumns": "11,11,24", "endOffsets": "202,509,652"}, "to": {"startLines": "28,34,3505", "startColumns": "4,4,4", "startOffsets": "936,1197,188021", "endLines": "31,41,3508", "endColumns": "11,11,24", "endOffsets": "1083,1499,188159"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\1b91be3af319ede480d7185430690ee1\\transformed\\lifecycle-viewmodel-2.7.0\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "49", "endOffsets": "100"}, "to": {"startLines": "369", "startColumns": "4", "startOffsets": "21745", "endColumns": "49", "endOffsets": "21790"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\5568a35fadd1ad6d1c703f16a16e5540\\transformed\\jetified-play-services-basement-18.4.0\\res\\values\\values.xml", "from": {"startLines": "4,7", "startColumns": "0,0", "startOffsets": "243,406", "endColumns": "63,166", "endOffsets": "306,572"}, "to": {"startLines": "375,422", "startColumns": "4,4", "startOffsets": "22102,25866", "endColumns": "67,166", "endOffsets": "22165,26028"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\8cf478dec41eed746328fa8046755ba2\\transformed\\core-1.13.1\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,98,99,103,104,105,106,112,122,155,176,209", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,115,187,275,340,406,475,538,608,676,748,818,879,953,1026,1087,1148,1210,1274,1336,1397,1465,1565,1625,1691,1764,1833,1890,1942,2004,2076,2152,2217,2276,2335,2395,2455,2515,2575,2635,2695,2755,2815,2875,2935,2994,3054,3114,3174,3234,3294,3354,3414,3474,3534,3594,3653,3713,3773,3832,3891,3950,4009,4068,4127,4162,4197,4252,4315,4370,4428,4486,4547,4610,4667,4718,4768,4829,4886,4952,4986,5021,5056,5126,5193,5265,5334,5403,5477,5549,5637,5708,5825,6026,6136,6337,6466,6538,6605,6808,7109,8840,9521,10203", "endLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,97,98,102,103,104,105,111,121,154,175,208,214", "endColumns": "59,71,87,64,65,68,62,69,67,71,69,60,73,72,60,60,61,63,61,60,67,99,59,65,72,68,56,51,61,71,75,64,58,58,59,59,59,59,59,59,59,59,59,59,58,59,59,59,59,59,59,59,59,59,59,58,59,59,58,58,58,58,58,58,34,34,54,62,54,57,57,60,62,56,50,49,60,56,65,33,34,34,69,66,71,68,68,73,71,87,70,116,12,109,12,128,71,66,24,24,24,24,24,24", "endOffsets": "110,182,270,335,401,470,533,603,671,743,813,874,948,1021,1082,1143,1205,1269,1331,1392,1460,1560,1620,1686,1759,1828,1885,1937,1999,2071,2147,2212,2271,2330,2390,2450,2510,2570,2630,2690,2750,2810,2870,2930,2989,3049,3109,3169,3229,3289,3349,3409,3469,3529,3589,3648,3708,3768,3827,3886,3945,4004,4063,4122,4157,4192,4247,4310,4365,4423,4481,4542,4605,4662,4713,4763,4824,4881,4947,4981,5016,5051,5121,5188,5260,5329,5398,5472,5544,5632,5703,5820,6021,6131,6332,6461,6533,6600,6803,7104,8835,9516,10198,10365"}, "to": {"startLines": "33,82,83,96,97,128,129,233,234,235,236,237,238,239,255,256,257,258,259,260,261,262,263,264,265,266,267,268,269,285,286,287,288,289,290,291,292,293,294,295,296,297,298,299,300,301,302,303,304,305,306,307,308,309,310,311,312,313,314,315,316,317,318,319,330,331,344,345,346,347,348,349,350,351,352,353,354,355,356,357,358,359,378,407,408,409,410,411,412,413,439,1981,1982,1987,1990,1995,2140,2141,2806,2851,3021,3054,3084,3117", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1137,3174,3246,4294,4359,6449,6518,13594,13664,13732,13804,13874,13935,14009,15252,15313,15374,15436,15500,15562,15623,15691,15791,15851,15917,15990,16059,16116,16168,17116,17188,17264,17329,17388,17447,17507,17567,17627,17687,17747,17807,17867,17927,17987,18047,18106,18166,18226,18286,18346,18406,18466,18526,18586,18646,18706,18765,18825,18885,18944,19003,19062,19121,19180,19748,19783,20431,20486,20549,20604,20662,20720,20781,20844,20901,20952,21002,21063,21120,21186,21220,21255,22296,24315,24382,24454,24523,24592,24666,24738,27491,128038,128155,128422,128715,128982,140473,140545,162424,164398,172233,173964,174964,175646", "endLines": "33,82,83,96,97,128,129,233,234,235,236,237,238,239,255,256,257,258,259,260,261,262,263,264,265,266,267,268,269,285,286,287,288,289,290,291,292,293,294,295,296,297,298,299,300,301,302,303,304,305,306,307,308,309,310,311,312,313,314,315,316,317,318,319,330,331,344,345,346,347,348,349,350,351,352,353,354,355,356,357,358,359,378,407,408,409,410,411,412,413,439,1981,1985,1987,1993,1995,2140,2141,2811,2860,3053,3074,3116,3122", "endColumns": "59,71,87,64,65,68,62,69,67,71,69,60,73,72,60,60,61,63,61,60,67,99,59,65,72,68,56,51,61,71,75,64,58,58,59,59,59,59,59,59,59,59,59,59,58,59,59,59,59,59,59,59,59,59,59,58,59,59,58,58,58,58,58,58,34,34,54,62,54,57,57,60,62,56,50,49,60,56,65,33,34,34,69,66,71,68,68,73,71,87,70,116,12,109,12,128,71,66,24,24,24,24,24,24", "endOffsets": "1192,3241,3329,4354,4420,6513,6576,13659,13727,13799,13869,13930,14004,14077,15308,15369,15431,15495,15557,15618,15686,15786,15846,15912,15985,16054,16111,16163,16225,17183,17259,17324,17383,17442,17502,17562,17622,17682,17742,17802,17862,17922,17982,18042,18101,18161,18221,18281,18341,18401,18461,18521,18581,18641,18701,18760,18820,18880,18939,18998,19057,19116,19175,19234,19778,19813,20481,20544,20599,20657,20715,20776,20839,20896,20947,20997,21058,21115,21181,21215,21250,21285,22361,24377,24449,24518,24587,24661,24733,24821,27557,128150,128351,128527,128911,129106,140540,140607,162622,164694,173959,174640,175641,175808"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\0098a6e93522fecc805d8900172003dc\\transformed\\preference-1.2.1\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,22,23,24,25,42,45,51,57,60,66,70,73,80,86,89,95,100,105,112,114,120,126,134,139,146,151,157,161,168,172,178,184,187,192,193,194,199,215,238,243,257,268,348,358,368,386,392,439,461,485", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,178,247,311,366,434,501,566,623,680,728,776,837,900,963,1001,1058,1102,1242,1381,1431,1479,2917,3022,3378,3716,3862,4202,4414,4577,4984,5322,5445,5784,6023,6280,6651,6711,7049,7335,7784,8076,8464,8769,9113,9358,9688,9895,10163,10436,10580,10949,10996,11052,11308,12367,13788,14126,15012,15622,20168,20687,21229,22503,22763,25467,26989,28470", "endLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,21,22,23,24,41,44,50,56,59,65,69,72,79,85,88,94,99,104,111,113,119,125,133,138,145,150,156,160,167,171,177,183,186,191,192,193,198,214,237,242,256,267,347,357,367,385,391,438,460,484,508", "endColumns": "72,68,63,54,67,66,64,56,56,47,47,60,62,62,37,56,43,13,138,49,47,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,46,55,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24", "endOffsets": "173,242,306,361,429,496,561,618,675,723,771,832,895,958,996,1053,1097,1237,1376,1426,1474,2912,3017,3373,3711,3857,4197,4409,4572,4979,5317,5440,5779,6018,6275,6646,6706,7044,7330,7779,8071,8459,8764,9108,9353,9683,9890,10158,10431,10575,10944,10991,11047,11303,12362,13783,14121,15007,15617,20163,20682,21224,22498,22758,25462,26984,28465,29984"}, "to": {"startLines": "74,131,270,271,272,273,274,275,276,334,335,336,376,377,432,433,434,435,440,441,442,1531,1719,1722,1728,1734,1737,1743,1747,1750,1757,1763,1766,1772,1777,1782,1789,1791,1797,1803,1811,1816,1823,1828,1834,1838,1845,1849,1855,1861,1864,1868,1869,2797,2812,2979,3017,3159,3347,3365,3429,3439,3449,3456,3462,3570,3739,3756", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "2600,6670,16230,16294,16349,16417,16484,16549,16606,19923,19971,20019,22170,22233,27159,27197,27254,27298,27562,27701,27751,97239,111213,111318,111563,111901,112047,112387,112599,112762,113169,113507,113630,113969,114208,114465,114836,114896,115234,115520,115969,116261,116649,116954,117298,117543,117873,118080,118348,118621,118765,118966,119013,162104,162627,170783,172084,177026,182936,183564,185489,185771,186076,186338,186598,190257,196552,197082", "endLines": "74,131,270,271,272,273,274,275,276,334,335,336,376,377,432,433,434,437,440,441,442,1547,1721,1727,1733,1736,1742,1746,1749,1756,1762,1765,1771,1776,1781,1788,1790,1796,1802,1810,1815,1822,1827,1833,1837,1844,1848,1854,1860,1863,1867,1868,1869,2801,2822,2998,3020,3168,3354,3428,3438,3448,3455,3461,3504,3582,3755,3772", "endColumns": "72,68,63,54,67,66,64,56,56,47,47,60,62,62,37,56,43,13,138,49,47,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,46,55,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24", "endOffsets": "2668,6734,16289,16344,16412,16479,16544,16601,16658,19966,20014,20075,22228,22291,27192,27249,27293,27433,27696,27746,27794,98672,111313,111558,111896,112042,112382,112594,112757,113164,113502,113625,113964,114203,114460,114831,114891,115229,115515,115964,116256,116644,116949,117293,117538,117868,118075,118343,118616,118760,118961,119008,119064,162284,163023,171507,172228,177353,183179,185484,185766,186071,186333,186593,188016,190704,197077,197645"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\2c7a28485314ee0e74d343b62dcecbb6\\transformed\\media-1.7.0\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11", "startColumns": "4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,144,215,288,350,410,476,598,659,725", "endColumns": "88,70,72,61,59,65,121,60,65,66", "endOffsets": "139,210,283,345,405,471,593,654,720,787"}, "to": {"startLines": "130,136,142,332,373,1986,1988,1989,1994,1996", "startColumns": "4,4,4,4,4,4,4,4,4,4", "startOffsets": "6581,7039,7442,19818,21985,128356,128532,128654,128916,129111", "endColumns": "88,70,72,61,59,65,121,60,65,66", "endOffsets": "6665,7105,7510,19875,22040,128417,128649,128710,128977,129173"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\f6fa56a75284f8e0ea5d27971291f441\\transformed\\jetified-play-services-base-18.5.0\\res\\values\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,33,46", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "215,301,377,463,549,625,702,778,951,1052,1233,1354,1457,1637,1756,1868,1967,2155,2256,2437,2558,2733,2877,2936,2994,3164,3475", "endLines": "4,5,6,7,8,9,10,11,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,45,64", "endColumns": "85,75,85,85,75,76,75,75,100,180,120,102,179,118,111,98,187,100,180,120,174,143,58,57,74,20,20", "endOffsets": "300,376,462,548,624,701,777,853,1051,1232,1353,1456,1636,1755,1867,1966,2154,2255,2436,2557,2732,2876,2935,2993,3068,3474,3887"}, "to": {"startLines": "98,99,100,101,102,103,104,105,414,415,416,417,418,419,420,421,423,424,425,426,427,428,429,430,431,3169,3583", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4425,4515,4595,4685,4775,4855,4936,5016,24826,24931,25112,25237,25344,25524,25647,25763,26033,26221,26326,26507,26632,26807,26955,27018,27080,177358,190709", "endLines": "98,99,100,101,102,103,104,105,414,415,416,417,418,419,420,421,423,424,425,426,427,428,429,430,431,3181,3601", "endColumns": "89,79,89,89,79,80,79,79,104,180,124,106,179,122,115,102,187,104,180,124,174,147,62,61,78,20,20", "endOffsets": "4510,4590,4680,4770,4850,4931,5011,5091,24926,25107,25232,25339,25519,25642,25758,25861,26216,26321,26502,26627,26802,26950,27013,27075,27154,177668,191121"}}, {"source": "C:\\Users\\<USER>\\Documents\\GitHub\\lc-moble-project\\lc_mobile_app\\android\\app\\src\\main\\res\\values\\styles.xml", "from": {"startLines": "3,18", "startColumns": "4,4", "startOffsets": "173,1093", "endLines": "11,20", "endColumns": "12,12", "endOffsets": "746,1257"}, "to": {"startLines": "1548,1556", "startColumns": "4,4", "startOffsets": "98677,99127", "endLines": "1555,1558", "endColumns": "12,12", "endOffsets": "99122,99291"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\e0d9d3675465ff69d847e2f781f20c61\\transformed\\jetified-startup-runtime-1.1.1\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "82", "endOffsets": "133"}, "to": {"startLines": "406", "startColumns": "4", "startOffsets": "24232", "endColumns": "82", "endOffsets": "24310"}}]}]}