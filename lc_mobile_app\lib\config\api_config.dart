class ApiConfig {
  // Base URL for the FastAPI backend
  // Windows Host WiFi IP: ************** (for Android device access)
  // WSL Gateway IP: ************ (for WSL to host access)
  // Android Emulator: ******** (for emulator localhost access)
  // Local WSL: ************** (if server runs in WSL)
  static const String baseUrl =
      'http://**************:8000'; // Windows host WiFi IP for Android device access

  // API endpoints
  static const String loginEndpoint = '/users/login';
  static const String registerEndpoint = '/users/register';
  static const String resetPasswordEndpoint = '/users/reset-password';
  static const String userProfileEndpoint = '/users/me';

  // Token key for shared preferences
  static const String tokenKey = 'auth_token';
}
