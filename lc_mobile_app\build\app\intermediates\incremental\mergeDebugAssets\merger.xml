<?xml version="1.0" encoding="utf-8"?>
<merger version="3"><dataSet config="com.google.mlkit:text-recognition:16.0.0" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5d9e93414479276552c10b05500a1b65\transformed\jetified-text-recognition-16.0.0\assets"><file name="mlkit-google-ocr-models/aksara/aksara_page_layout_analysis_rpn_gcn.binarypb" path="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5d9e93414479276552c10b05500a1b65\transformed\jetified-text-recognition-16.0.0\assets\mlkit-google-ocr-models\aksara\aksara_page_layout_analysis_rpn_gcn.binarypb"/><file name="mlkit-google-ocr-models/aksara/aksara_page_layout_analysis_ti_rpn_gcn.binarypb" path="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5d9e93414479276552c10b05500a1b65\transformed\jetified-text-recognition-16.0.0\assets\mlkit-google-ocr-models\aksara\aksara_page_layout_analysis_ti_rpn_gcn.binarypb"/><file name="mlkit-google-ocr-models/gocr/gocr_models/line_recognition_legacy_mobile/Latn_ctc/optical/assets.extra/LabelMap.pb" path="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5d9e93414479276552c10b05500a1b65\transformed\jetified-text-recognition-16.0.0\assets\mlkit-google-ocr-models\gocr\gocr_models\line_recognition_legacy_mobile\Latn_ctc\optical\assets.extra\LabelMap.pb"/><file name="mlkit-google-ocr-models/gocr/gocr_models/line_recognition_legacy_mobile/Latn_ctc/optical/conv_model.fb" path="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5d9e93414479276552c10b05500a1b65\transformed\jetified-text-recognition-16.0.0\assets\mlkit-google-ocr-models\gocr\gocr_models\line_recognition_legacy_mobile\Latn_ctc\optical\conv_model.fb"/><file name="mlkit-google-ocr-models/gocr/gocr_models/line_recognition_legacy_mobile/Latn_ctc/optical/lstm_model.fb" path="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5d9e93414479276552c10b05500a1b65\transformed\jetified-text-recognition-16.0.0\assets\mlkit-google-ocr-models\gocr\gocr_models\line_recognition_legacy_mobile\Latn_ctc\optical\lstm_model.fb"/><file name="mlkit-google-ocr-models/gocr/gocr_models/line_recognition_legacy_mobile/Latn_ctc_cpu.binarypb" path="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5d9e93414479276552c10b05500a1b65\transformed\jetified-text-recognition-16.0.0\assets\mlkit-google-ocr-models\gocr\gocr_models\line_recognition_legacy_mobile\Latn_ctc_cpu.binarypb"/><file name="mlkit-google-ocr-models/gocr/gocr_models/line_recognition_legacy_mobile/tflite_langid.tflite" path="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5d9e93414479276552c10b05500a1b65\transformed\jetified-text-recognition-16.0.0\assets\mlkit-google-ocr-models\gocr\gocr_models\line_recognition_legacy_mobile\tflite_langid.tflite"/><file name="mlkit-google-ocr-models/gocr/layout/line_clustering_custom_ops/model.tflite" path="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5d9e93414479276552c10b05500a1b65\transformed\jetified-text-recognition-16.0.0\assets\mlkit-google-ocr-models\gocr\layout\line_clustering_custom_ops\model.tflite"/><file name="mlkit-google-ocr-models/gocr/layout/line_splitting_custom_ops/model.tflite" path="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5d9e93414479276552c10b05500a1b65\transformed\jetified-text-recognition-16.0.0\assets\mlkit-google-ocr-models\gocr\layout\line_splitting_custom_ops\model.tflite"/><file name="mlkit-google-ocr-models/taser/detector/region_proposal_text_detector_tflite_vertical_mbv2_v1.bincfg" path="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5d9e93414479276552c10b05500a1b65\transformed\jetified-text-recognition-16.0.0\assets\mlkit-google-ocr-models\taser\detector\region_proposal_text_detector_tflite_vertical_mbv2_v1.bincfg"/><file name="mlkit-google-ocr-models/taser/detector/rpn_text_detector_mobile_space_to_depth_quantized_mbv2_v1.tflite" path="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5d9e93414479276552c10b05500a1b65\transformed\jetified-text-recognition-16.0.0\assets\mlkit-google-ocr-models\taser\detector\rpn_text_detector_mobile_space_to_depth_quantized_mbv2_v1.tflite"/><file name="mlkit-google-ocr-models/taser/rpn_text_detection_tflite_mobile_mbv2.binarypb" path="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5d9e93414479276552c10b05500a1b65\transformed\jetified-text-recognition-16.0.0\assets\mlkit-google-ocr-models\taser\rpn_text_detection_tflite_mobile_mbv2.binarypb"/><file name="mlkit-google-ocr-models/taser/segmenter/tflite_script_detector_0.3.bincfg" path="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5d9e93414479276552c10b05500a1b65\transformed\jetified-text-recognition-16.0.0\assets\mlkit-google-ocr-models\taser\segmenter\tflite_script_detector_0.3.bincfg"/><file name="mlkit-google-ocr-models/taser/segmenter/tflite_script_detector_0.3.conv_model" path="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5d9e93414479276552c10b05500a1b65\transformed\jetified-text-recognition-16.0.0\assets\mlkit-google-ocr-models\taser\segmenter\tflite_script_detector_0.3.conv_model"/><file name="mlkit-google-ocr-models/taser/segmenter/tflite_script_detector_0.3.lstm_model" path="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5d9e93414479276552c10b05500a1b65\transformed\jetified-text-recognition-16.0.0\assets\mlkit-google-ocr-models\taser\segmenter\tflite_script_detector_0.3.lstm_model"/><file name="mlkit-google-ocr-models/taser/taser_script_identification_tflite_mobile.binarypb" path="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5d9e93414479276552c10b05500a1b65\transformed\jetified-text-recognition-16.0.0\assets\mlkit-google-ocr-models\taser\taser_script_identification_tflite_mobile.binarypb"/><file name="mlkit-google-ocr-models/taser_tflite_gocrlatin_mbv2_scriptid_aksara_layout_gcn_mobile_engine.binarypb" path="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5d9e93414479276552c10b05500a1b65\transformed\jetified-text-recognition-16.0.0\assets\mlkit-google-ocr-models\taser_tflite_gocrlatin_mbv2_scriptid_aksara_layout_gcn_mobile_engine.binarypb"/><file name="mlkit-google-ocr-models/taser_tflite_gocrlatin_mbv2_scriptid_aksara_layout_gcn_mobile_engine_ti.binarypb" path="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5d9e93414479276552c10b05500a1b65\transformed\jetified-text-recognition-16.0.0\assets\mlkit-google-ocr-models\taser_tflite_gocrlatin_mbv2_scriptid_aksara_layout_gcn_mobile_engine_ti.binarypb"/><file name="mlkit-google-ocr-models/taser_tflite_gocrlatin_mbv2_scriptid_aksara_layout_gcn_mobile_recognizer.binarypb" path="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5d9e93414479276552c10b05500a1b65\transformed\jetified-text-recognition-16.0.0\assets\mlkit-google-ocr-models\taser_tflite_gocrlatin_mbv2_scriptid_aksara_layout_gcn_mobile_recognizer.binarypb"/><file name="mlkit-google-ocr-models/taser_tflite_gocrlatin_mbv2_scriptid_aksara_layout_gcn_mobile_runner.binarypb" path="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5d9e93414479276552c10b05500a1b65\transformed\jetified-text-recognition-16.0.0\assets\mlkit-google-ocr-models\taser_tflite_gocrlatin_mbv2_scriptid_aksara_layout_gcn_mobile_runner.binarypb"/><file name="mlkit-google-ocr-models/taser_tflite_gocrlatin_mbv2_scriptid_aksara_layout_gcn_mobile_runner_ti.binarypb" path="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5d9e93414479276552c10b05500a1b65\transformed\jetified-text-recognition-16.0.0\assets\mlkit-google-ocr-models\taser_tflite_gocrlatin_mbv2_scriptid_aksara_layout_gcn_mobile_runner_ti.binarypb"/></source></dataSet><dataSet config=":path_provider_android" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\Documents\GitHub\lc-moble-project\lc_mobile_app\build\path_provider_android\intermediates\library_assets\debug\packageDebugAssets\out"/></dataSet><dataSet config=":google_mlkit_commons" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\Documents\GitHub\lc-moble-project\lc_mobile_app\build\google_mlkit_commons\intermediates\library_assets\debug\packageDebugAssets\out"/></dataSet><dataSet config=":google_mlkit_text_recognition" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\Documents\GitHub\lc-moble-project\lc_mobile_app\build\google_mlkit_text_recognition\intermediates\library_assets\debug\packageDebugAssets\out"/></dataSet><dataSet config=":flutter_plugin_android_lifecycle" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\Documents\GitHub\lc-moble-project\lc_mobile_app\build\flutter_plugin_android_lifecycle\intermediates\library_assets\debug\packageDebugAssets\out"/></dataSet><dataSet config=":flutter_native_splash" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\Documents\GitHub\lc-moble-project\lc_mobile_app\build\flutter_native_splash\intermediates\library_assets\debug\packageDebugAssets\out"/></dataSet><dataSet config=":image_picker_android" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\Documents\GitHub\lc-moble-project\lc_mobile_app\build\image_picker_android\intermediates\library_assets\debug\packageDebugAssets\out"/></dataSet><dataSet config=":shared_preferences_android" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\Documents\GitHub\lc-moble-project\lc_mobile_app\build\shared_preferences_android\intermediates\library_assets\debug\packageDebugAssets\out"/></dataSet><dataSet config=":camera_android_camerax" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\Documents\GitHub\lc-moble-project\lc_mobile_app\build\camera_android_camerax\intermediates\library_assets\debug\packageDebugAssets\out"/></dataSet><dataSet config="main" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\Documents\GitHub\lc-moble-project\lc_mobile_app\android\app\src\main\assets"/></dataSet><dataSet config="debug" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\Documents\GitHub\lc-moble-project\lc_mobile_app\android\app\src\debug\assets"/></dataSet><dataSet config="generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\Documents\GitHub\lc-moble-project\lc_mobile_app\build\app\intermediates\shader_assets\debug\compileDebugShaders\out"/></dataSet></merger>