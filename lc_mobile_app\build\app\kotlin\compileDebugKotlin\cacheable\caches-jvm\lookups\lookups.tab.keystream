  Manifest android  
permission android.Manifest  CAMERA android.Manifest.permission  Activity android.app  Array android.app.Activity  Bundle android.app.Activity  
FlutterEngine android.app.Activity  Int android.app.Activity  IntArray android.app.Activity  
MethodChannel android.app.Activity  PermissionHandler android.app.Activity  String android.app.Activity  configureFlutterEngine android.app.Activity  invoke android.app.Activity  onCreate android.app.Activity  onRequestPermissionsResult android.app.Activity  Context android.content  Array android.content.Context  Bundle android.content.Context  
FlutterEngine android.content.Context  Int android.content.Context  IntArray android.content.Context  
MethodChannel android.content.Context  PermissionHandler android.content.Context  String android.content.Context  configureFlutterEngine android.content.Context  invoke android.content.Context  onCreate android.content.Context  onRequestPermissionsResult android.content.Context  Array android.content.ContextWrapper  Bundle android.content.ContextWrapper  
FlutterEngine android.content.ContextWrapper  Int android.content.ContextWrapper  IntArray android.content.ContextWrapper  
MethodChannel android.content.ContextWrapper  PermissionHandler android.content.ContextWrapper  String android.content.ContextWrapper  configureFlutterEngine android.content.ContextWrapper  invoke android.content.ContextWrapper  onCreate android.content.ContextWrapper  onRequestPermissionsResult android.content.ContextWrapper  PackageManager android.content.pm  PERMISSION_GRANTED !android.content.pm.PackageManager  Bundle 
android.os  Array  android.view.ContextThemeWrapper  Bundle  android.view.ContextThemeWrapper  
FlutterEngine  android.view.ContextThemeWrapper  Int  android.view.ContextThemeWrapper  IntArray  android.view.ContextThemeWrapper  
MethodChannel  android.view.ContextThemeWrapper  PermissionHandler  android.view.ContextThemeWrapper  String  android.view.ContextThemeWrapper  configureFlutterEngine  android.view.ContextThemeWrapper  invoke  android.view.ContextThemeWrapper  onCreate  android.view.ContextThemeWrapper  onRequestPermissionsResult  android.view.ContextThemeWrapper  ActivityCompat androidx.core.app  requestPermissions  androidx.core.app.ActivityCompat  
ContextCompat androidx.core.content  checkSelfPermission #androidx.core.content.ContextCompat  ActivityCompat com.example.lc_mobile_app  Array com.example.lc_mobile_app  Boolean com.example.lc_mobile_app  	Companion com.example.lc_mobile_app  
ContextCompat com.example.lc_mobile_app  Int com.example.lc_mobile_app  IntArray com.example.lc_mobile_app  MainActivity com.example.lc_mobile_app  Manifest com.example.lc_mobile_app  
MethodChannel com.example.lc_mobile_app  PackageManager com.example.lc_mobile_app  PermissionHandler com.example.lc_mobile_app  String com.example.lc_mobile_app  arrayOf com.example.lc_mobile_app  invoke com.example.lc_mobile_app  
isNotEmpty com.example.lc_mobile_app  Array &com.example.lc_mobile_app.MainActivity  Bundle &com.example.lc_mobile_app.MainActivity  
FlutterEngine &com.example.lc_mobile_app.MainActivity  Int &com.example.lc_mobile_app.MainActivity  IntArray &com.example.lc_mobile_app.MainActivity  
MethodChannel &com.example.lc_mobile_app.MainActivity  PERMISSION_CHANNEL &com.example.lc_mobile_app.MainActivity  PermissionHandler &com.example.lc_mobile_app.MainActivity  String &com.example.lc_mobile_app.MainActivity  invoke &com.example.lc_mobile_app.MainActivity  permissionHandler &com.example.lc_mobile_app.MainActivity  Activity +com.example.lc_mobile_app.PermissionHandler  ActivityCompat +com.example.lc_mobile_app.PermissionHandler  Array +com.example.lc_mobile_app.PermissionHandler  Boolean +com.example.lc_mobile_app.PermissionHandler  CAMERA_PERMISSION_REQUEST_CODE +com.example.lc_mobile_app.PermissionHandler  	Companion +com.example.lc_mobile_app.PermissionHandler  
ContextCompat +com.example.lc_mobile_app.PermissionHandler  Int +com.example.lc_mobile_app.PermissionHandler  IntArray +com.example.lc_mobile_app.PermissionHandler  Manifest +com.example.lc_mobile_app.PermissionHandler  
MethodCall +com.example.lc_mobile_app.PermissionHandler  
MethodChannel +com.example.lc_mobile_app.PermissionHandler  PackageManager +com.example.lc_mobile_app.PermissionHandler  String +com.example.lc_mobile_app.PermissionHandler  activity +com.example.lc_mobile_app.PermissionHandler  arrayOf +com.example.lc_mobile_app.PermissionHandler  
getARRAYOf +com.example.lc_mobile_app.PermissionHandler  
getArrayOf +com.example.lc_mobile_app.PermissionHandler  
getISNotEmpty +com.example.lc_mobile_app.PermissionHandler  
getIsNotEmpty +com.example.lc_mobile_app.PermissionHandler  handleMethodCall +com.example.lc_mobile_app.PermissionHandler  handlePermissionResult +com.example.lc_mobile_app.PermissionHandler  hasCameraPermission +com.example.lc_mobile_app.PermissionHandler  
isNotEmpty +com.example.lc_mobile_app.PermissionHandler  requestCameraPermission +com.example.lc_mobile_app.PermissionHandler  Activity 5com.example.lc_mobile_app.PermissionHandler.Companion  ActivityCompat 5com.example.lc_mobile_app.PermissionHandler.Companion  Array 5com.example.lc_mobile_app.PermissionHandler.Companion  Boolean 5com.example.lc_mobile_app.PermissionHandler.Companion  	Companion 5com.example.lc_mobile_app.PermissionHandler.Companion  
ContextCompat 5com.example.lc_mobile_app.PermissionHandler.Companion  Int 5com.example.lc_mobile_app.PermissionHandler.Companion  IntArray 5com.example.lc_mobile_app.PermissionHandler.Companion  Manifest 5com.example.lc_mobile_app.PermissionHandler.Companion  
MethodCall 5com.example.lc_mobile_app.PermissionHandler.Companion  
MethodChannel 5com.example.lc_mobile_app.PermissionHandler.Companion  PackageManager 5com.example.lc_mobile_app.PermissionHandler.Companion  String 5com.example.lc_mobile_app.PermissionHandler.Companion  arrayOf 5com.example.lc_mobile_app.PermissionHandler.Companion  
getARRAYOf 5com.example.lc_mobile_app.PermissionHandler.Companion  
getArrayOf 5com.example.lc_mobile_app.PermissionHandler.Companion  
getISNotEmpty 5com.example.lc_mobile_app.PermissionHandler.Companion  
getIsNotEmpty 5com.example.lc_mobile_app.PermissionHandler.Companion  invoke 5com.example.lc_mobile_app.PermissionHandler.Companion  
isNotEmpty 5com.example.lc_mobile_app.PermissionHandler.Companion  permissionResult 5com.example.lc_mobile_app.PermissionHandler.Companion  FlutterActivity io.flutter.embedding.android  Array ,io.flutter.embedding.android.FlutterActivity  Bundle ,io.flutter.embedding.android.FlutterActivity  
FlutterEngine ,io.flutter.embedding.android.FlutterActivity  Int ,io.flutter.embedding.android.FlutterActivity  IntArray ,io.flutter.embedding.android.FlutterActivity  
MethodChannel ,io.flutter.embedding.android.FlutterActivity  PermissionHandler ,io.flutter.embedding.android.FlutterActivity  String ,io.flutter.embedding.android.FlutterActivity  configureFlutterEngine ,io.flutter.embedding.android.FlutterActivity  invoke ,io.flutter.embedding.android.FlutterActivity  onCreate ,io.flutter.embedding.android.FlutterActivity  onRequestPermissionsResult ,io.flutter.embedding.android.FlutterActivity  
FlutterEngine io.flutter.embedding.engine  dartExecutor )io.flutter.embedding.engine.FlutterEngine  getDARTExecutor )io.flutter.embedding.engine.FlutterEngine  getDartExecutor )io.flutter.embedding.engine.FlutterEngine  setDartExecutor )io.flutter.embedding.engine.FlutterEngine  binaryMessenger -io.flutter.embedding.engine.dart.DartExecutor  getBINARYMessenger -io.flutter.embedding.engine.dart.DartExecutor  getBinaryMessenger -io.flutter.embedding.engine.dart.DartExecutor  setBinaryMessenger -io.flutter.embedding.engine.dart.DartExecutor  BinaryMessenger io.flutter.plugin.common  
MethodCall io.flutter.plugin.common  
MethodChannel io.flutter.plugin.common  method #io.flutter.plugin.common.MethodCall  Result &io.flutter.plugin.common.MethodChannel  setMethodCallHandler &io.flutter.plugin.common.MethodChannel  <SAM-CONSTRUCTOR> 8io.flutter.plugin.common.MethodChannel.MethodCallHandler  notImplemented -io.flutter.plugin.common.MethodChannel.Result  success -io.flutter.plugin.common.MethodChannel.Result  ActivityCompat 	java.lang  	Companion 	java.lang  
ContextCompat 	java.lang  Manifest 	java.lang  
MethodChannel 	java.lang  PackageManager 	java.lang  PermissionHandler 	java.lang  arrayOf 	java.lang  
isNotEmpty 	java.lang  ActivityCompat kotlin  Array kotlin  Boolean kotlin  	Companion kotlin  
ContextCompat kotlin  	Function2 kotlin  Int kotlin  IntArray kotlin  Manifest kotlin  
MethodChannel kotlin  Nothing kotlin  PackageManager kotlin  PermissionHandler kotlin  String kotlin  arrayOf kotlin  
isNotEmpty kotlin  
getISNotEmpty kotlin.IntArray  
getIsNotEmpty kotlin.IntArray  
isNotEmpty kotlin.IntArray  ActivityCompat kotlin.annotation  	Companion kotlin.annotation  
ContextCompat kotlin.annotation  Manifest kotlin.annotation  
MethodChannel kotlin.annotation  PackageManager kotlin.annotation  PermissionHandler kotlin.annotation  arrayOf kotlin.annotation  
isNotEmpty kotlin.annotation  ActivityCompat kotlin.collections  	Companion kotlin.collections  
ContextCompat kotlin.collections  Manifest kotlin.collections  
MethodChannel kotlin.collections  PackageManager kotlin.collections  PermissionHandler kotlin.collections  arrayOf kotlin.collections  
isNotEmpty kotlin.collections  ActivityCompat kotlin.comparisons  	Companion kotlin.comparisons  
ContextCompat kotlin.comparisons  Manifest kotlin.comparisons  
MethodChannel kotlin.comparisons  PackageManager kotlin.comparisons  PermissionHandler kotlin.comparisons  arrayOf kotlin.comparisons  
isNotEmpty kotlin.comparisons  ActivityCompat 	kotlin.io  	Companion 	kotlin.io  
ContextCompat 	kotlin.io  Manifest 	kotlin.io  
MethodChannel 	kotlin.io  PackageManager 	kotlin.io  PermissionHandler 	kotlin.io  arrayOf 	kotlin.io  
isNotEmpty 	kotlin.io  ActivityCompat 
kotlin.jvm  	Companion 
kotlin.jvm  
ContextCompat 
kotlin.jvm  Manifest 
kotlin.jvm  
MethodChannel 
kotlin.jvm  PackageManager 
kotlin.jvm  PermissionHandler 
kotlin.jvm  arrayOf 
kotlin.jvm  
isNotEmpty 
kotlin.jvm  ActivityCompat 
kotlin.ranges  	Companion 
kotlin.ranges  
ContextCompat 
kotlin.ranges  Manifest 
kotlin.ranges  
MethodChannel 
kotlin.ranges  PackageManager 
kotlin.ranges  PermissionHandler 
kotlin.ranges  arrayOf 
kotlin.ranges  
isNotEmpty 
kotlin.ranges  ActivityCompat kotlin.sequences  	Companion kotlin.sequences  
ContextCompat kotlin.sequences  Manifest kotlin.sequences  
MethodChannel kotlin.sequences  PackageManager kotlin.sequences  PermissionHandler kotlin.sequences  arrayOf kotlin.sequences  
isNotEmpty kotlin.sequences  ActivityCompat kotlin.text  	Companion kotlin.text  
ContextCompat kotlin.text  Manifest kotlin.text  
MethodChannel kotlin.text  PackageManager kotlin.text  PermissionHandler kotlin.text  arrayOf kotlin.text  
isNotEmpty kotlin.text                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                 