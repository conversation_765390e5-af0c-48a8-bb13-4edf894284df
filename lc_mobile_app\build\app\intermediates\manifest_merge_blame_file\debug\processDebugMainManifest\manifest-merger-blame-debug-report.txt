1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.example.lc_mobile_app"
4    android:versionCode="1"
5    android:versionName="1.0.0" >
6
7    <uses-sdk
8        android:minSdkVersion="24"
9        android:targetSdkVersion="35" />
10    <!--
11         The INTERNET permission is required for development. Specifically,
12         the Flutter tool needs it to communicate with the running application
13         to allow setting breakpoints, to provide hot reload, etc.
14    -->
15    <uses-permission android:name="android.permission.INTERNET" />
15-->C:\Users\<USER>\Documents\GitHub\lc-moble-project\lc_mobile_app\android\app\src\main\AndroidManifest.xml:7:5-67
15-->C:\Users\<USER>\Documents\GitHub\lc-moble-project\lc_mobile_app\android\app\src\main\AndroidManifest.xml:7:22-64
16    <!-- Camera permissions -->
17    <uses-permission android:name="android.permission.CAMERA" />
17-->C:\Users\<USER>\Documents\GitHub\lc-moble-project\lc_mobile_app\android\app\src\main\AndroidManifest.xml:3:5-65
17-->C:\Users\<USER>\Documents\GitHub\lc-moble-project\lc_mobile_app\android\app\src\main\AndroidManifest.xml:3:22-62
18
19    <uses-feature android:name="android.hardware.camera" />
19-->C:\Users\<USER>\Documents\GitHub\lc-moble-project\lc_mobile_app\android\app\src\main\AndroidManifest.xml:4:5-60
19-->C:\Users\<USER>\Documents\GitHub\lc-moble-project\lc_mobile_app\android\app\src\main\AndroidManifest.xml:4:19-57
20    <uses-feature android:name="android.hardware.camera.autofocus" /> <!-- External storage permission -->
20-->C:\Users\<USER>\Documents\GitHub\lc-moble-project\lc_mobile_app\android\app\src\main\AndroidManifest.xml:5:5-70
20-->C:\Users\<USER>\Documents\GitHub\lc-moble-project\lc_mobile_app\android\app\src\main\AndroidManifest.xml:5:19-67
21    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
21-->C:\Users\<USER>\Documents\GitHub\lc-moble-project\lc_mobile_app\android\app\src\main\AndroidManifest.xml:9:5-80
21-->C:\Users\<USER>\Documents\GitHub\lc-moble-project\lc_mobile_app\android\app\src\main\AndroidManifest.xml:9:22-77
22    <uses-permission
22-->C:\Users\<USER>\Documents\GitHub\lc-moble-project\lc_mobile_app\android\app\src\main\AndroidManifest.xml:10:5-81
23        android:name="android.permission.WRITE_EXTERNAL_STORAGE"
23-->C:\Users\<USER>\Documents\GitHub\lc-moble-project\lc_mobile_app\android\app\src\main\AndroidManifest.xml:10:22-78
24        android:maxSdkVersion="28" />
24-->[:camera_android_camerax] C:\Users\<USER>\Documents\GitHub\lc-moble-project\lc_mobile_app\build\camera_android_camerax\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:13:9-35
25    <!--
26 Required to query activities that can process text, see:
27         https://developer.android.com/training/package-visibility and
28         https://developer.android.com/reference/android/content/Intent#ACTION_PROCESS_TEXT.
29
30         In particular, this is used by the Flutter engine in io.flutter.plugin.text.ProcessTextPlugin.
31    -->
32    <queries>
32-->C:\Users\<USER>\Documents\GitHub\lc-moble-project\lc_mobile_app\android\app\src\main\AndroidManifest.xml:49:5-54:15
33        <intent>
33-->C:\Users\<USER>\Documents\GitHub\lc-moble-project\lc_mobile_app\android\app\src\main\AndroidManifest.xml:50:9-53:18
34            <action android:name="android.intent.action.PROCESS_TEXT" />
34-->C:\Users\<USER>\Documents\GitHub\lc-moble-project\lc_mobile_app\android\app\src\main\AndroidManifest.xml:51:13-72
34-->C:\Users\<USER>\Documents\GitHub\lc-moble-project\lc_mobile_app\android\app\src\main\AndroidManifest.xml:51:21-70
35
36            <data android:mimeType="text/plain" />
36-->C:\Users\<USER>\Documents\GitHub\lc-moble-project\lc_mobile_app\android\app\src\main\AndroidManifest.xml:52:13-50
36-->C:\Users\<USER>\Documents\GitHub\lc-moble-project\lc_mobile_app\android\app\src\main\AndroidManifest.xml:52:19-48
37        </intent>
38    </queries>
39
40    <uses-feature android:name="android.hardware.camera.any" />
40-->[:camera_android_camerax] C:\Users\<USER>\Documents\GitHub\lc-moble-project\lc_mobile_app\build\camera_android_camerax\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-64
40-->[:camera_android_camerax] C:\Users\<USER>\Documents\GitHub\lc-moble-project\lc_mobile_app\build\camera_android_camerax\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:19-61
41
42    <uses-permission android:name="android.permission.RECORD_AUDIO" />
42-->[:camera_android_camerax] C:\Users\<USER>\Documents\GitHub\lc-moble-project\lc_mobile_app\build\camera_android_camerax\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:5-71
42-->[:camera_android_camerax] C:\Users\<USER>\Documents\GitHub\lc-moble-project\lc_mobile_app\build\camera_android_camerax\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:22-68
43
44    <permission
44-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8cf478dec41eed746328fa8046755ba2\transformed\core-1.13.1\AndroidManifest.xml:22:5-24:47
45        android:name="com.example.lc_mobile_app.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
45-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8cf478dec41eed746328fa8046755ba2\transformed\core-1.13.1\AndroidManifest.xml:23:9-81
46        android:protectionLevel="signature" />
46-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8cf478dec41eed746328fa8046755ba2\transformed\core-1.13.1\AndroidManifest.xml:24:9-44
47
48    <uses-permission android:name="com.example.lc_mobile_app.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" /> <!-- Although the *SdkVersion is captured in gradle build files, this is required for non gradle builds -->
48-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8cf478dec41eed746328fa8046755ba2\transformed\core-1.13.1\AndroidManifest.xml:26:5-97
48-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8cf478dec41eed746328fa8046755ba2\transformed\core-1.13.1\AndroidManifest.xml:26:22-94
49    <!-- <uses-sdk android:minSdkVersion="14"/> -->
50    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" /> <!-- Bluetooth permissions -->
50-->[com.google.android.datatransport:transport-backend-cct:2.3.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3fe589753a4aba9a36c72c795b877407\transformed\jetified-transport-backend-cct-2.3.3\AndroidManifest.xml:25:5-79
50-->[com.google.android.datatransport:transport-backend-cct:2.3.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3fe589753a4aba9a36c72c795b877407\transformed\jetified-transport-backend-cct-2.3.3\AndroidManifest.xml:25:22-76
51    <application
52        android:name="android.app.Application"
53        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
53-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8cf478dec41eed746328fa8046755ba2\transformed\core-1.13.1\AndroidManifest.xml:28:18-86
54        android:debuggable="true"
55        android:extractNativeLibs="false"
56        android:icon="@mipmap/ic_launcher"
57        android:label="lc_mobile_app" >
58        <activity
59            android:name="com.example.lc_mobile_app.MainActivity"
60            android:configChanges="orientation|keyboardHidden|keyboard|screenSize|smallestScreenSize|locale|layoutDirection|fontScale|screenLayout|density|uiMode"
61            android:exported="true"
62            android:hardwareAccelerated="true"
63            android:launchMode="singleTop"
64            android:taskAffinity=""
65            android:theme="@style/LaunchTheme"
66            android:windowSoftInputMode="adjustResize" >
67
68            <!--
69                 Specifies an Android theme to apply to this Activity as soon as
70                 the Android process has started. This theme is visible to the user
71                 while the Flutter UI initializes. After that, this theme continues
72                 to determine the Window background behind the Flutter UI.
73            -->
74            <meta-data
75                android:name="io.flutter.embedding.android.NormalTheme"
76                android:resource="@style/NormalTheme" />
77
78            <intent-filter>
79                <action android:name="android.intent.action.MAIN" />
80
81                <category android:name="android.intent.category.LAUNCHER" />
82            </intent-filter>
83        </activity>
84        <!--
85             Don't delete the meta-data below.
86             This is used by the Flutter tool to generate GeneratedPluginRegistrant.java
87        -->
88        <meta-data
89            android:name="flutterEmbedding"
90            android:value="2" />
91
92        <service
92-->[androidx.camera:camera-camera2:1.5.0-beta01] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\65f8a731866b2db1fdba6bbeea6a3bba\transformed\jetified-camera-camera2-1.5.0-beta01\AndroidManifest.xml:24:9-33:19
93            android:name="androidx.camera.core.impl.MetadataHolderService"
93-->[androidx.camera:camera-camera2:1.5.0-beta01] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\65f8a731866b2db1fdba6bbeea6a3bba\transformed\jetified-camera-camera2-1.5.0-beta01\AndroidManifest.xml:25:13-75
94            android:enabled="false"
94-->[androidx.camera:camera-camera2:1.5.0-beta01] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\65f8a731866b2db1fdba6bbeea6a3bba\transformed\jetified-camera-camera2-1.5.0-beta01\AndroidManifest.xml:26:13-36
95            android:exported="false" >
95-->[androidx.camera:camera-camera2:1.5.0-beta01] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\65f8a731866b2db1fdba6bbeea6a3bba\transformed\jetified-camera-camera2-1.5.0-beta01\AndroidManifest.xml:27:13-37
96            <meta-data
96-->[androidx.camera:camera-camera2:1.5.0-beta01] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\65f8a731866b2db1fdba6bbeea6a3bba\transformed\jetified-camera-camera2-1.5.0-beta01\AndroidManifest.xml:30:13-32:89
97                android:name="androidx.camera.core.impl.MetadataHolderService.DEFAULT_CONFIG_PROVIDER"
97-->[androidx.camera:camera-camera2:1.5.0-beta01] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\65f8a731866b2db1fdba6bbeea6a3bba\transformed\jetified-camera-camera2-1.5.0-beta01\AndroidManifest.xml:31:17-103
98                android:value="androidx.camera.camera2.Camera2Config$DefaultProvider" />
98-->[androidx.camera:camera-camera2:1.5.0-beta01] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\65f8a731866b2db1fdba6bbeea6a3bba\transformed\jetified-camera-camera2-1.5.0-beta01\AndroidManifest.xml:32:17-86
99        </service>
100
101        <provider
101-->[:image_picker_android] C:\Users\<USER>\Documents\GitHub\lc-moble-project\lc_mobile_app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:9-17:20
102            android:name="io.flutter.plugins.imagepicker.ImagePickerFileProvider"
102-->[:image_picker_android] C:\Users\<USER>\Documents\GitHub\lc-moble-project\lc_mobile_app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:13-82
103            android:authorities="com.example.lc_mobile_app.flutter.image_provider"
103-->[:image_picker_android] C:\Users\<USER>\Documents\GitHub\lc-moble-project\lc_mobile_app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:13-74
104            android:exported="false"
104-->[:image_picker_android] C:\Users\<USER>\Documents\GitHub\lc-moble-project\lc_mobile_app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:12:13-37
105            android:grantUriPermissions="true" >
105-->[:image_picker_android] C:\Users\<USER>\Documents\GitHub\lc-moble-project\lc_mobile_app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:13:13-47
106            <meta-data
106-->[:image_picker_android] C:\Users\<USER>\Documents\GitHub\lc-moble-project\lc_mobile_app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:14:13-16:75
107                android:name="android.support.FILE_PROVIDER_PATHS"
107-->[:image_picker_android] C:\Users\<USER>\Documents\GitHub\lc-moble-project\lc_mobile_app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:15:17-67
108                android:resource="@xml/flutter_image_picker_file_paths" />
108-->[:image_picker_android] C:\Users\<USER>\Documents\GitHub\lc-moble-project\lc_mobile_app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:16:17-72
109        </provider> <!-- Trigger Google Play services to install the backported photo picker module. -->
110        <service
110-->[:image_picker_android] C:\Users\<USER>\Documents\GitHub\lc-moble-project\lc_mobile_app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:19:9-31:19
111            android:name="com.google.android.gms.metadata.ModuleDependencies"
111-->[:image_picker_android] C:\Users\<USER>\Documents\GitHub\lc-moble-project\lc_mobile_app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:20:13-78
112            android:enabled="false"
112-->[:image_picker_android] C:\Users\<USER>\Documents\GitHub\lc-moble-project\lc_mobile_app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:21:13-36
113            android:exported="false" >
113-->[:image_picker_android] C:\Users\<USER>\Documents\GitHub\lc-moble-project\lc_mobile_app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:22:13-37
114            <intent-filter>
114-->[:image_picker_android] C:\Users\<USER>\Documents\GitHub\lc-moble-project\lc_mobile_app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:24:13-26:29
115                <action android:name="com.google.android.gms.metadata.MODULE_DEPENDENCIES" />
115-->[:image_picker_android] C:\Users\<USER>\Documents\GitHub\lc-moble-project\lc_mobile_app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:25:17-94
115-->[:image_picker_android] C:\Users\<USER>\Documents\GitHub\lc-moble-project\lc_mobile_app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:25:25-91
116            </intent-filter>
117
118            <meta-data
118-->[:image_picker_android] C:\Users\<USER>\Documents\GitHub\lc-moble-project\lc_mobile_app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:28:13-30:36
119                android:name="photopicker_activity:0:required"
119-->[:image_picker_android] C:\Users\<USER>\Documents\GitHub\lc-moble-project\lc_mobile_app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:29:17-63
120                android:value="" />
120-->[:image_picker_android] C:\Users\<USER>\Documents\GitHub\lc-moble-project\lc_mobile_app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:30:17-33
121        </service>
122        <service
122-->[com.google.android.gms:play-services-mlkit-text-recognition-common:19.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e3b2794a35f1e89257bb8548354ece59\transformed\jetified-play-services-mlkit-text-recognition-common-19.0.0\AndroidManifest.xml:9:9-15:19
123            android:name="com.google.mlkit.common.internal.MlKitComponentDiscoveryService"
123-->[com.google.android.gms:play-services-mlkit-text-recognition-common:19.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e3b2794a35f1e89257bb8548354ece59\transformed\jetified-play-services-mlkit-text-recognition-common-19.0.0\AndroidManifest.xml:10:13-91
124            android:directBootAware="true"
124-->[com.google.mlkit:common:18.8.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\10185d9885b3817450f62bda0903273b\transformed\jetified-common-18.8.0\AndroidManifest.xml:17:13-43
125            android:exported="false" >
125-->[com.google.android.gms:play-services-mlkit-text-recognition-common:19.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e3b2794a35f1e89257bb8548354ece59\transformed\jetified-play-services-mlkit-text-recognition-common-19.0.0\AndroidManifest.xml:11:13-37
126            <meta-data
126-->[com.google.android.gms:play-services-mlkit-text-recognition-common:19.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e3b2794a35f1e89257bb8548354ece59\transformed\jetified-play-services-mlkit-text-recognition-common-19.0.0\AndroidManifest.xml:12:13-14:85
127                android:name="com.google.firebase.components:com.google.mlkit.vision.text.internal.TextRegistrar"
127-->[com.google.android.gms:play-services-mlkit-text-recognition-common:19.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e3b2794a35f1e89257bb8548354ece59\transformed\jetified-play-services-mlkit-text-recognition-common-19.0.0\AndroidManifest.xml:13:17-114
128                android:value="com.google.firebase.components.ComponentRegistrar" />
128-->[com.google.android.gms:play-services-mlkit-text-recognition-common:19.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e3b2794a35f1e89257bb8548354ece59\transformed\jetified-play-services-mlkit-text-recognition-common-19.0.0\AndroidManifest.xml:14:17-82
129            <meta-data
129-->[com.google.mlkit:vision-common:17.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a05b5895d4441831cef9203768ec4801\transformed\jetified-vision-common-17.3.0\AndroidManifest.xml:12:13-14:85
130                android:name="com.google.firebase.components:com.google.mlkit.vision.common.internal.VisionCommonRegistrar"
130-->[com.google.mlkit:vision-common:17.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a05b5895d4441831cef9203768ec4801\transformed\jetified-vision-common-17.3.0\AndroidManifest.xml:13:17-124
131                android:value="com.google.firebase.components.ComponentRegistrar" />
131-->[com.google.mlkit:vision-common:17.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a05b5895d4441831cef9203768ec4801\transformed\jetified-vision-common-17.3.0\AndroidManifest.xml:14:17-82
132            <meta-data
132-->[com.google.mlkit:common:18.8.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\10185d9885b3817450f62bda0903273b\transformed\jetified-common-18.8.0\AndroidManifest.xml:20:13-22:85
133                android:name="com.google.firebase.components:com.google.mlkit.common.internal.CommonComponentRegistrar"
133-->[com.google.mlkit:common:18.8.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\10185d9885b3817450f62bda0903273b\transformed\jetified-common-18.8.0\AndroidManifest.xml:21:17-120
134                android:value="com.google.firebase.components.ComponentRegistrar" />
134-->[com.google.mlkit:common:18.8.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\10185d9885b3817450f62bda0903273b\transformed\jetified-common-18.8.0\AndroidManifest.xml:22:17-82
135        </service>
136
137        <provider
137-->[com.google.mlkit:common:18.8.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\10185d9885b3817450f62bda0903273b\transformed\jetified-common-18.8.0\AndroidManifest.xml:9:9-13:38
138            android:name="com.google.mlkit.common.internal.MlKitInitProvider"
138-->[com.google.mlkit:common:18.8.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\10185d9885b3817450f62bda0903273b\transformed\jetified-common-18.8.0\AndroidManifest.xml:10:13-78
139            android:authorities="com.example.lc_mobile_app.mlkitinitprovider"
139-->[com.google.mlkit:common:18.8.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\10185d9885b3817450f62bda0903273b\transformed\jetified-common-18.8.0\AndroidManifest.xml:11:13-69
140            android:exported="false"
140-->[com.google.mlkit:common:18.8.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\10185d9885b3817450f62bda0903273b\transformed\jetified-common-18.8.0\AndroidManifest.xml:12:13-37
141            android:initOrder="99" />
141-->[com.google.mlkit:common:18.8.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\10185d9885b3817450f62bda0903273b\transformed\jetified-common-18.8.0\AndroidManifest.xml:13:13-35
142
143        <activity
143-->[com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\80d381bf084c21e18706da6716588126\transformed\jetified-play-services-base-18.1.0\AndroidManifest.xml:20:9-22:45
144            android:name="com.google.android.gms.common.api.GoogleApiActivity"
144-->[com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\80d381bf084c21e18706da6716588126\transformed\jetified-play-services-base-18.1.0\AndroidManifest.xml:20:19-85
145            android:exported="false"
145-->[com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\80d381bf084c21e18706da6716588126\transformed\jetified-play-services-base-18.1.0\AndroidManifest.xml:22:19-43
146            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
146-->[com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\80d381bf084c21e18706da6716588126\transformed\jetified-play-services-base-18.1.0\AndroidManifest.xml:21:19-78
147
148        <meta-data
148-->[com.google.android.gms:play-services-basement:18.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7d9977a1c54ea44a3347b382d148518d\transformed\jetified-play-services-basement-18.1.0\AndroidManifest.xml:21:9-23:69
149            android:name="com.google.android.gms.version"
149-->[com.google.android.gms:play-services-basement:18.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7d9977a1c54ea44a3347b382d148518d\transformed\jetified-play-services-basement-18.1.0\AndroidManifest.xml:22:13-58
150            android:value="@integer/google_play_services_version" />
150-->[com.google.android.gms:play-services-basement:18.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7d9977a1c54ea44a3347b382d148518d\transformed\jetified-play-services-basement-18.1.0\AndroidManifest.xml:23:13-66
151
152        <uses-library
152-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5093ab42d2307deb2d7ac0b7f5718c38\transformed\jetified-window-1.2.0\AndroidManifest.xml:23:9-25:40
153            android:name="androidx.window.extensions"
153-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5093ab42d2307deb2d7ac0b7f5718c38\transformed\jetified-window-1.2.0\AndroidManifest.xml:24:13-54
154            android:required="false" />
154-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5093ab42d2307deb2d7ac0b7f5718c38\transformed\jetified-window-1.2.0\AndroidManifest.xml:25:13-37
155        <uses-library
155-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5093ab42d2307deb2d7ac0b7f5718c38\transformed\jetified-window-1.2.0\AndroidManifest.xml:26:9-28:40
156            android:name="androidx.window.sidecar"
156-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5093ab42d2307deb2d7ac0b7f5718c38\transformed\jetified-window-1.2.0\AndroidManifest.xml:27:13-51
157            android:required="false" />
157-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5093ab42d2307deb2d7ac0b7f5718c38\transformed\jetified-window-1.2.0\AndroidManifest.xml:28:13-37
158
159        <provider
159-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e80aa748ff6540407d8dba61c0d945fe\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:24:9-32:20
160            android:name="androidx.startup.InitializationProvider"
160-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e80aa748ff6540407d8dba61c0d945fe\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:25:13-67
161            android:authorities="com.example.lc_mobile_app.androidx-startup"
161-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e80aa748ff6540407d8dba61c0d945fe\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:26:13-68
162            android:exported="false" >
162-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e80aa748ff6540407d8dba61c0d945fe\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:27:13-37
163            <meta-data
163-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e80aa748ff6540407d8dba61c0d945fe\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:29:13-31:52
164                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
164-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e80aa748ff6540407d8dba61c0d945fe\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:30:17-78
165                android:value="androidx.startup" />
165-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e80aa748ff6540407d8dba61c0d945fe\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:31:17-49
166            <meta-data
166-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f7dcf6815bacc3ae7f099ba801348de\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:29:13-31:52
167                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
167-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f7dcf6815bacc3ae7f099ba801348de\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:30:17-85
168                android:value="androidx.startup" />
168-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f7dcf6815bacc3ae7f099ba801348de\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:31:17-49
169        </provider>
170
171        <receiver
171-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f7dcf6815bacc3ae7f099ba801348de\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:34:9-52:20
172            android:name="androidx.profileinstaller.ProfileInstallReceiver"
172-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f7dcf6815bacc3ae7f099ba801348de\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:35:13-76
173            android:directBootAware="false"
173-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f7dcf6815bacc3ae7f099ba801348de\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:36:13-44
174            android:enabled="true"
174-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f7dcf6815bacc3ae7f099ba801348de\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:37:13-35
175            android:exported="true"
175-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f7dcf6815bacc3ae7f099ba801348de\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:38:13-36
176            android:permission="android.permission.DUMP" >
176-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f7dcf6815bacc3ae7f099ba801348de\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:39:13-57
177            <intent-filter>
177-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f7dcf6815bacc3ae7f099ba801348de\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:40:13-42:29
178                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
178-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f7dcf6815bacc3ae7f099ba801348de\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:41:17-91
178-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f7dcf6815bacc3ae7f099ba801348de\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:41:25-88
179            </intent-filter>
180            <intent-filter>
180-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f7dcf6815bacc3ae7f099ba801348de\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:43:13-45:29
181                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
181-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f7dcf6815bacc3ae7f099ba801348de\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:44:17-85
181-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f7dcf6815bacc3ae7f099ba801348de\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:44:25-82
182            </intent-filter>
183            <intent-filter>
183-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f7dcf6815bacc3ae7f099ba801348de\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:46:13-48:29
184                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
184-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f7dcf6815bacc3ae7f099ba801348de\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:47:17-88
184-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f7dcf6815bacc3ae7f099ba801348de\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:47:25-85
185            </intent-filter>
186            <intent-filter>
186-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f7dcf6815bacc3ae7f099ba801348de\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:49:13-51:29
187                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
187-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f7dcf6815bacc3ae7f099ba801348de\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:50:17-95
187-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f7dcf6815bacc3ae7f099ba801348de\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:50:25-92
188            </intent-filter>
189        </receiver>
190
191        <service
191-->[com.google.android.datatransport:transport-backend-cct:2.3.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3fe589753a4aba9a36c72c795b877407\transformed\jetified-transport-backend-cct-2.3.3\AndroidManifest.xml:29:9-35:19
192            android:name="com.google.android.datatransport.runtime.backends.TransportBackendDiscovery"
192-->[com.google.android.datatransport:transport-backend-cct:2.3.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3fe589753a4aba9a36c72c795b877407\transformed\jetified-transport-backend-cct-2.3.3\AndroidManifest.xml:30:13-103
193            android:exported="false" >
193-->[com.google.android.datatransport:transport-backend-cct:2.3.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3fe589753a4aba9a36c72c795b877407\transformed\jetified-transport-backend-cct-2.3.3\AndroidManifest.xml:31:13-37
194            <meta-data
194-->[com.google.android.datatransport:transport-backend-cct:2.3.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3fe589753a4aba9a36c72c795b877407\transformed\jetified-transport-backend-cct-2.3.3\AndroidManifest.xml:32:13-34:39
195                android:name="backend:com.google.android.datatransport.cct.CctBackendFactory"
195-->[com.google.android.datatransport:transport-backend-cct:2.3.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3fe589753a4aba9a36c72c795b877407\transformed\jetified-transport-backend-cct-2.3.3\AndroidManifest.xml:33:17-94
196                android:value="cct" />
196-->[com.google.android.datatransport:transport-backend-cct:2.3.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3fe589753a4aba9a36c72c795b877407\transformed\jetified-transport-backend-cct-2.3.3\AndroidManifest.xml:34:17-36
197        </service>
198        <service
198-->[com.google.android.datatransport:transport-runtime:2.2.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9fb76887d9728a5f4c19fa6f805116ec\transformed\jetified-transport-runtime-2.2.6\AndroidManifest.xml:26:9-30:19
199            android:name="com.google.android.datatransport.runtime.scheduling.jobscheduling.JobInfoSchedulerService"
199-->[com.google.android.datatransport:transport-runtime:2.2.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9fb76887d9728a5f4c19fa6f805116ec\transformed\jetified-transport-runtime-2.2.6\AndroidManifest.xml:27:13-117
200            android:exported="false"
200-->[com.google.android.datatransport:transport-runtime:2.2.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9fb76887d9728a5f4c19fa6f805116ec\transformed\jetified-transport-runtime-2.2.6\AndroidManifest.xml:28:13-37
201            android:permission="android.permission.BIND_JOB_SERVICE" >
201-->[com.google.android.datatransport:transport-runtime:2.2.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9fb76887d9728a5f4c19fa6f805116ec\transformed\jetified-transport-runtime-2.2.6\AndroidManifest.xml:29:13-69
202        </service>
203
204        <receiver
204-->[com.google.android.datatransport:transport-runtime:2.2.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9fb76887d9728a5f4c19fa6f805116ec\transformed\jetified-transport-runtime-2.2.6\AndroidManifest.xml:32:9-34:40
205            android:name="com.google.android.datatransport.runtime.scheduling.jobscheduling.AlarmManagerSchedulerBroadcastReceiver"
205-->[com.google.android.datatransport:transport-runtime:2.2.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9fb76887d9728a5f4c19fa6f805116ec\transformed\jetified-transport-runtime-2.2.6\AndroidManifest.xml:33:13-132
206            android:exported="false" />
206-->[com.google.android.datatransport:transport-runtime:2.2.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9fb76887d9728a5f4c19fa6f805116ec\transformed\jetified-transport-runtime-2.2.6\AndroidManifest.xml:34:13-37
207    </application>
208
209</manifest>
