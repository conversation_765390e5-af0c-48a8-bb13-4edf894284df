1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.example.lc_mobile_app"
4    android:versionCode="1"
5    android:versionName="1.0.0" >
6
7    <uses-sdk
8        android:minSdkVersion="24"
9        android:targetSdkVersion="35" />
10    <!--
11         The INTERNET permission is required for development. Specifically,
12         the Flutter tool needs it to communicate with the running application
13         to allow setting breakpoints, to provide hot reload, etc.
14    -->
15    <uses-permission android:name="android.permission.INTERNET" />
15-->C:\Users\<USER>\Documents\GitHub\lc-moble-project\lc_mobile_app\android\app\src\main\AndroidManifest.xml:7:5-67
15-->C:\Users\<USER>\Documents\GitHub\lc-moble-project\lc_mobile_app\android\app\src\main\AndroidManifest.xml:7:22-64
16    <!-- Camera permissions -->
17    <uses-permission android:name="android.permission.CAMERA" />
17-->C:\Users\<USER>\Documents\GitHub\lc-moble-project\lc_mobile_app\android\app\src\main\AndroidManifest.xml:3:5-65
17-->C:\Users\<USER>\Documents\GitHub\lc-moble-project\lc_mobile_app\android\app\src\main\AndroidManifest.xml:3:22-62
18
19    <uses-feature android:name="android.hardware.camera" />
19-->C:\Users\<USER>\Documents\GitHub\lc-moble-project\lc_mobile_app\android\app\src\main\AndroidManifest.xml:4:5-60
19-->C:\Users\<USER>\Documents\GitHub\lc-moble-project\lc_mobile_app\android\app\src\main\AndroidManifest.xml:4:19-57
20    <uses-feature android:name="android.hardware.camera.autofocus" /> <!-- External storage permission -->
20-->C:\Users\<USER>\Documents\GitHub\lc-moble-project\lc_mobile_app\android\app\src\main\AndroidManifest.xml:5:5-70
20-->C:\Users\<USER>\Documents\GitHub\lc-moble-project\lc_mobile_app\android\app\src\main\AndroidManifest.xml:5:19-67
21    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
21-->C:\Users\<USER>\Documents\GitHub\lc-moble-project\lc_mobile_app\android\app\src\main\AndroidManifest.xml:9:5-80
21-->C:\Users\<USER>\Documents\GitHub\lc-moble-project\lc_mobile_app\android\app\src\main\AndroidManifest.xml:9:22-77
22    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
22-->C:\Users\<USER>\Documents\GitHub\lc-moble-project\lc_mobile_app\android\app\src\main\AndroidManifest.xml:10:5-81
22-->C:\Users\<USER>\Documents\GitHub\lc-moble-project\lc_mobile_app\android\app\src\main\AndroidManifest.xml:10:22-78
23    <!--
24 Required to query activities that can process text, see:
25         https://developer.android.com/training/package-visibility and
26         https://developer.android.com/reference/android/content/Intent#ACTION_PROCESS_TEXT.
27
28         In particular, this is used by the Flutter engine in io.flutter.plugin.text.ProcessTextPlugin.
29    -->
30    <queries>
30-->C:\Users\<USER>\Documents\GitHub\lc-moble-project\lc_mobile_app\android\app\src\main\AndroidManifest.xml:49:5-54:15
31        <intent>
31-->C:\Users\<USER>\Documents\GitHub\lc-moble-project\lc_mobile_app\android\app\src\main\AndroidManifest.xml:50:9-53:18
32            <action android:name="android.intent.action.PROCESS_TEXT" />
32-->C:\Users\<USER>\Documents\GitHub\lc-moble-project\lc_mobile_app\android\app\src\main\AndroidManifest.xml:51:13-72
32-->C:\Users\<USER>\Documents\GitHub\lc-moble-project\lc_mobile_app\android\app\src\main\AndroidManifest.xml:51:21-70
33
34            <data android:mimeType="text/plain" />
34-->C:\Users\<USER>\Documents\GitHub\lc-moble-project\lc_mobile_app\android\app\src\main\AndroidManifest.xml:52:13-50
34-->C:\Users\<USER>\Documents\GitHub\lc-moble-project\lc_mobile_app\android\app\src\main\AndroidManifest.xml:52:19-48
35        </intent>
36        <intent>
36-->[androidx.camera:camera-extensions:1.4.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\600370f70b5cbf9b173314ffcedf59d2\transformed\jetified-camera-extensions-1.4.2\AndroidManifest.xml:23:9-25:18
37            <action android:name="androidx.camera.extensions.action.VENDOR_ACTION" />
37-->[androidx.camera:camera-extensions:1.4.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\600370f70b5cbf9b173314ffcedf59d2\transformed\jetified-camera-extensions-1.4.2\AndroidManifest.xml:24:13-86
37-->[androidx.camera:camera-extensions:1.4.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\600370f70b5cbf9b173314ffcedf59d2\transformed\jetified-camera-extensions-1.4.2\AndroidManifest.xml:24:21-83
38        </intent>
39
40        <package android:name="androidx.test.orchestrator" />
40-->[androidx.test:runner:1.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ac6687a4d5466a0ef1e00e1845849b8b\transformed\runner-1.6.1\AndroidManifest.xml:25:9-62
40-->[androidx.test:runner:1.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ac6687a4d5466a0ef1e00e1845849b8b\transformed\runner-1.6.1\AndroidManifest.xml:25:18-59
41        <package android:name="androidx.test.services" />
41-->[androidx.test:runner:1.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ac6687a4d5466a0ef1e00e1845849b8b\transformed\runner-1.6.1\AndroidManifest.xml:26:9-58
41-->[androidx.test:runner:1.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ac6687a4d5466a0ef1e00e1845849b8b\transformed\runner-1.6.1\AndroidManifest.xml:26:18-55
42        <package android:name="com.google.android.apps.common.testing.services" />
42-->[androidx.test:runner:1.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ac6687a4d5466a0ef1e00e1845849b8b\transformed\runner-1.6.1\AndroidManifest.xml:27:9-83
42-->[androidx.test:runner:1.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ac6687a4d5466a0ef1e00e1845849b8b\transformed\runner-1.6.1\AndroidManifest.xml:27:18-80
43    </queries>
44
45    <uses-permission android:name="android.permission.RECORD_AUDIO" />
45-->[:camera_android] C:\Users\<USER>\Documents\GitHub\lc-moble-project\lc_mobile_app\build\camera_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:5-71
45-->[:camera_android] C:\Users\<USER>\Documents\GitHub\lc-moble-project\lc_mobile_app\build\camera_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:22-68
46
47    <permission
47-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8cf478dec41eed746328fa8046755ba2\transformed\core-1.13.1\AndroidManifest.xml:22:5-24:47
48        android:name="com.example.lc_mobile_app.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
48-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8cf478dec41eed746328fa8046755ba2\transformed\core-1.13.1\AndroidManifest.xml:23:9-81
49        android:protectionLevel="signature" />
49-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8cf478dec41eed746328fa8046755ba2\transformed\core-1.13.1\AndroidManifest.xml:24:9-44
50
51    <uses-permission android:name="com.example.lc_mobile_app.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" /> <!-- Although the *SdkVersion is captured in gradle build files, this is required for non gradle builds -->
51-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8cf478dec41eed746328fa8046755ba2\transformed\core-1.13.1\AndroidManifest.xml:26:5-97
51-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8cf478dec41eed746328fa8046755ba2\transformed\core-1.13.1\AndroidManifest.xml:26:22-94
52    <!-- <uses-sdk android:minSdkVersion="14"/> -->
53    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" /> <!-- Bluetooth permissions -->
53-->[com.google.android.datatransport:transport-backend-cct:2.3.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3fe589753a4aba9a36c72c795b877407\transformed\jetified-transport-backend-cct-2.3.3\AndroidManifest.xml:25:5-79
53-->[com.google.android.datatransport:transport-backend-cct:2.3.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3fe589753a4aba9a36c72c795b877407\transformed\jetified-transport-backend-cct-2.3.3\AndroidManifest.xml:25:22-76
54    <application
55        android:name="android.app.Application"
56        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
56-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8cf478dec41eed746328fa8046755ba2\transformed\core-1.13.1\AndroidManifest.xml:28:18-86
57        android:debuggable="true"
58        android:extractNativeLibs="false"
59        android:icon="@mipmap/ic_launcher"
60        android:label="lc_mobile_app" >
61        <activity
62            android:name="com.example.lc_mobile_app.MainActivity"
63            android:configChanges="orientation|keyboardHidden|keyboard|screenSize|smallestScreenSize|locale|layoutDirection|fontScale|screenLayout|density|uiMode"
64            android:exported="true"
65            android:hardwareAccelerated="true"
66            android:launchMode="singleTop"
67            android:taskAffinity=""
68            android:theme="@style/LaunchTheme"
69            android:windowSoftInputMode="adjustResize" >
70
71            <!--
72                 Specifies an Android theme to apply to this Activity as soon as
73                 the Android process has started. This theme is visible to the user
74                 while the Flutter UI initializes. After that, this theme continues
75                 to determine the Window background behind the Flutter UI.
76            -->
77            <meta-data
78                android:name="io.flutter.embedding.android.NormalTheme"
79                android:resource="@style/NormalTheme" />
80
81            <intent-filter>
82                <action android:name="android.intent.action.MAIN" />
83
84                <category android:name="android.intent.category.LAUNCHER" />
85            </intent-filter>
86        </activity>
87        <!--
88             Don't delete the meta-data below.
89             This is used by the Flutter tool to generate GeneratedPluginRegistrant.java
90        -->
91        <meta-data
92            android:name="flutterEmbedding"
93            android:value="2" />
94
95        <service android:name="io.apparence.camerawesome.buttons.PlayerService" />
95-->[:camerawesome] C:\Users\<USER>\Documents\GitHub\lc-moble-project\lc_mobile_app\build\camerawesome\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:9-83
95-->[:camerawesome] C:\Users\<USER>\Documents\GitHub\lc-moble-project\lc_mobile_app\build\camerawesome\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:18-80
96
97        <uses-library
97-->[androidx.camera:camera-extensions:1.4.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\600370f70b5cbf9b173314ffcedf59d2\transformed\jetified-camera-extensions-1.4.2\AndroidManifest.xml:29:9-31:40
98            android:name="androidx.camera.extensions.impl"
98-->[androidx.camera:camera-extensions:1.4.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\600370f70b5cbf9b173314ffcedf59d2\transformed\jetified-camera-extensions-1.4.2\AndroidManifest.xml:30:13-59
99            android:required="false" />
99-->[androidx.camera:camera-extensions:1.4.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\600370f70b5cbf9b173314ffcedf59d2\transformed\jetified-camera-extensions-1.4.2\AndroidManifest.xml:31:13-37
100
101        <service
101-->[androidx.camera:camera-camera2:1.4.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f811f807f7b17e979bbe8ae3001ddc19\transformed\jetified-camera-camera2-1.4.2\AndroidManifest.xml:24:9-33:19
102            android:name="androidx.camera.core.impl.MetadataHolderService"
102-->[androidx.camera:camera-camera2:1.4.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f811f807f7b17e979bbe8ae3001ddc19\transformed\jetified-camera-camera2-1.4.2\AndroidManifest.xml:25:13-75
103            android:enabled="false"
103-->[androidx.camera:camera-camera2:1.4.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f811f807f7b17e979bbe8ae3001ddc19\transformed\jetified-camera-camera2-1.4.2\AndroidManifest.xml:26:13-36
104            android:exported="false" >
104-->[androidx.camera:camera-camera2:1.4.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f811f807f7b17e979bbe8ae3001ddc19\transformed\jetified-camera-camera2-1.4.2\AndroidManifest.xml:27:13-37
105            <meta-data
105-->[androidx.camera:camera-camera2:1.4.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f811f807f7b17e979bbe8ae3001ddc19\transformed\jetified-camera-camera2-1.4.2\AndroidManifest.xml:30:13-32:89
106                android:name="androidx.camera.core.impl.MetadataHolderService.DEFAULT_CONFIG_PROVIDER"
106-->[androidx.camera:camera-camera2:1.4.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f811f807f7b17e979bbe8ae3001ddc19\transformed\jetified-camera-camera2-1.4.2\AndroidManifest.xml:31:17-103
107                android:value="androidx.camera.camera2.Camera2Config$DefaultProvider" />
107-->[androidx.camera:camera-camera2:1.4.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f811f807f7b17e979bbe8ae3001ddc19\transformed\jetified-camera-camera2-1.4.2\AndroidManifest.xml:32:17-86
108        </service>
109
110        <provider
110-->[:image_picker_android] C:\Users\<USER>\Documents\GitHub\lc-moble-project\lc_mobile_app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:9-17:20
111            android:name="io.flutter.plugins.imagepicker.ImagePickerFileProvider"
111-->[:image_picker_android] C:\Users\<USER>\Documents\GitHub\lc-moble-project\lc_mobile_app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:13-82
112            android:authorities="com.example.lc_mobile_app.flutter.image_provider"
112-->[:image_picker_android] C:\Users\<USER>\Documents\GitHub\lc-moble-project\lc_mobile_app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:13-74
113            android:exported="false"
113-->[:image_picker_android] C:\Users\<USER>\Documents\GitHub\lc-moble-project\lc_mobile_app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:12:13-37
114            android:grantUriPermissions="true" >
114-->[:image_picker_android] C:\Users\<USER>\Documents\GitHub\lc-moble-project\lc_mobile_app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:13:13-47
115            <meta-data
115-->[:image_picker_android] C:\Users\<USER>\Documents\GitHub\lc-moble-project\lc_mobile_app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:14:13-16:75
116                android:name="android.support.FILE_PROVIDER_PATHS"
116-->[:image_picker_android] C:\Users\<USER>\Documents\GitHub\lc-moble-project\lc_mobile_app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:15:17-67
117                android:resource="@xml/flutter_image_picker_file_paths" />
117-->[:image_picker_android] C:\Users\<USER>\Documents\GitHub\lc-moble-project\lc_mobile_app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:16:17-72
118        </provider> <!-- Trigger Google Play services to install the backported photo picker module. -->
119        <service
119-->[:image_picker_android] C:\Users\<USER>\Documents\GitHub\lc-moble-project\lc_mobile_app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:19:9-31:19
120            android:name="com.google.android.gms.metadata.ModuleDependencies"
120-->[:image_picker_android] C:\Users\<USER>\Documents\GitHub\lc-moble-project\lc_mobile_app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:20:13-78
121            android:enabled="false"
121-->[:image_picker_android] C:\Users\<USER>\Documents\GitHub\lc-moble-project\lc_mobile_app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:21:13-36
122            android:exported="false" >
122-->[:image_picker_android] C:\Users\<USER>\Documents\GitHub\lc-moble-project\lc_mobile_app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:22:13-37
123            <intent-filter>
123-->[:image_picker_android] C:\Users\<USER>\Documents\GitHub\lc-moble-project\lc_mobile_app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:24:13-26:29
124                <action android:name="com.google.android.gms.metadata.MODULE_DEPENDENCIES" />
124-->[:image_picker_android] C:\Users\<USER>\Documents\GitHub\lc-moble-project\lc_mobile_app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:25:17-94
124-->[:image_picker_android] C:\Users\<USER>\Documents\GitHub\lc-moble-project\lc_mobile_app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:25:25-91
125            </intent-filter>
126
127            <meta-data
127-->[:image_picker_android] C:\Users\<USER>\Documents\GitHub\lc-moble-project\lc_mobile_app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:28:13-30:36
128                android:name="photopicker_activity:0:required"
128-->[:image_picker_android] C:\Users\<USER>\Documents\GitHub\lc-moble-project\lc_mobile_app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:29:17-63
129                android:value="" />
129-->[:image_picker_android] C:\Users\<USER>\Documents\GitHub\lc-moble-project\lc_mobile_app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:30:17-33
130        </service>
131
132        <provider
132-->[:open_file_android] C:\Users\<USER>\Documents\GitHub\lc-moble-project\lc_mobile_app\build\open_file_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:9-19:20
133            android:name="com.crazecoder.openfile.FileProvider"
133-->[:open_file_android] C:\Users\<USER>\Documents\GitHub\lc-moble-project\lc_mobile_app\build\open_file_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:13-64
134            android:authorities="com.example.lc_mobile_app.fileProvider.com.crazecoder.openfile"
134-->[:open_file_android] C:\Users\<USER>\Documents\GitHub\lc-moble-project\lc_mobile_app\build\open_file_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:13-88
135            android:exported="false"
135-->[:open_file_android] C:\Users\<USER>\Documents\GitHub\lc-moble-project\lc_mobile_app\build\open_file_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:12:13-37
136            android:grantUriPermissions="true"
136-->[:open_file_android] C:\Users\<USER>\Documents\GitHub\lc-moble-project\lc_mobile_app\build\open_file_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:13:13-47
137            android:requestLegacyExternalStorage="true" >
137-->[:open_file_android] C:\Users\<USER>\Documents\GitHub\lc-moble-project\lc_mobile_app\build\open_file_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:14:13-56
138            <meta-data
138-->[:image_picker_android] C:\Users\<USER>\Documents\GitHub\lc-moble-project\lc_mobile_app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:14:13-16:75
139                android:name="android.support.FILE_PROVIDER_PATHS"
139-->[:image_picker_android] C:\Users\<USER>\Documents\GitHub\lc-moble-project\lc_mobile_app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:15:17-67
140                android:resource="@xml/filepaths" />
140-->[:image_picker_android] C:\Users\<USER>\Documents\GitHub\lc-moble-project\lc_mobile_app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:16:17-72
141        </provider>
142
143        <service
143-->[com.google.android.gms:play-services-mlkit-text-recognition-common:19.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e3b2794a35f1e89257bb8548354ece59\transformed\jetified-play-services-mlkit-text-recognition-common-19.0.0\AndroidManifest.xml:9:9-15:19
144            android:name="com.google.mlkit.common.internal.MlKitComponentDiscoveryService"
144-->[com.google.android.gms:play-services-mlkit-text-recognition-common:19.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e3b2794a35f1e89257bb8548354ece59\transformed\jetified-play-services-mlkit-text-recognition-common-19.0.0\AndroidManifest.xml:10:13-91
145            android:directBootAware="true"
145-->[com.google.mlkit:common:18.8.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\10185d9885b3817450f62bda0903273b\transformed\jetified-common-18.8.0\AndroidManifest.xml:17:13-43
146            android:exported="false" >
146-->[com.google.android.gms:play-services-mlkit-text-recognition-common:19.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e3b2794a35f1e89257bb8548354ece59\transformed\jetified-play-services-mlkit-text-recognition-common-19.0.0\AndroidManifest.xml:11:13-37
147            <meta-data
147-->[com.google.android.gms:play-services-mlkit-text-recognition-common:19.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e3b2794a35f1e89257bb8548354ece59\transformed\jetified-play-services-mlkit-text-recognition-common-19.0.0\AndroidManifest.xml:12:13-14:85
148                android:name="com.google.firebase.components:com.google.mlkit.vision.text.internal.TextRegistrar"
148-->[com.google.android.gms:play-services-mlkit-text-recognition-common:19.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e3b2794a35f1e89257bb8548354ece59\transformed\jetified-play-services-mlkit-text-recognition-common-19.0.0\AndroidManifest.xml:13:17-114
149                android:value="com.google.firebase.components.ComponentRegistrar" />
149-->[com.google.android.gms:play-services-mlkit-text-recognition-common:19.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e3b2794a35f1e89257bb8548354ece59\transformed\jetified-play-services-mlkit-text-recognition-common-19.0.0\AndroidManifest.xml:14:17-82
150            <meta-data
150-->[com.google.mlkit:vision-common:17.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a05b5895d4441831cef9203768ec4801\transformed\jetified-vision-common-17.3.0\AndroidManifest.xml:12:13-14:85
151                android:name="com.google.firebase.components:com.google.mlkit.vision.common.internal.VisionCommonRegistrar"
151-->[com.google.mlkit:vision-common:17.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a05b5895d4441831cef9203768ec4801\transformed\jetified-vision-common-17.3.0\AndroidManifest.xml:13:17-124
152                android:value="com.google.firebase.components.ComponentRegistrar" />
152-->[com.google.mlkit:vision-common:17.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a05b5895d4441831cef9203768ec4801\transformed\jetified-vision-common-17.3.0\AndroidManifest.xml:14:17-82
153            <meta-data
153-->[com.google.mlkit:common:18.8.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\10185d9885b3817450f62bda0903273b\transformed\jetified-common-18.8.0\AndroidManifest.xml:20:13-22:85
154                android:name="com.google.firebase.components:com.google.mlkit.common.internal.CommonComponentRegistrar"
154-->[com.google.mlkit:common:18.8.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\10185d9885b3817450f62bda0903273b\transformed\jetified-common-18.8.0\AndroidManifest.xml:21:17-120
155                android:value="com.google.firebase.components.ComponentRegistrar" />
155-->[com.google.mlkit:common:18.8.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\10185d9885b3817450f62bda0903273b\transformed\jetified-common-18.8.0\AndroidManifest.xml:22:17-82
156        </service>
157
158        <provider
158-->[com.google.mlkit:common:18.8.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\10185d9885b3817450f62bda0903273b\transformed\jetified-common-18.8.0\AndroidManifest.xml:9:9-13:38
159            android:name="com.google.mlkit.common.internal.MlKitInitProvider"
159-->[com.google.mlkit:common:18.8.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\10185d9885b3817450f62bda0903273b\transformed\jetified-common-18.8.0\AndroidManifest.xml:10:13-78
160            android:authorities="com.example.lc_mobile_app.mlkitinitprovider"
160-->[com.google.mlkit:common:18.8.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\10185d9885b3817450f62bda0903273b\transformed\jetified-common-18.8.0\AndroidManifest.xml:11:13-69
161            android:exported="false"
161-->[com.google.mlkit:common:18.8.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\10185d9885b3817450f62bda0903273b\transformed\jetified-common-18.8.0\AndroidManifest.xml:12:13-37
162            android:initOrder="99" />
162-->[com.google.mlkit:common:18.8.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\10185d9885b3817450f62bda0903273b\transformed\jetified-common-18.8.0\AndroidManifest.xml:13:13-35
163
164        <activity
164-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f6fa56a75284f8e0ea5d27971291f441\transformed\jetified-play-services-base-18.5.0\AndroidManifest.xml:5:9-173
165            android:name="com.google.android.gms.common.api.GoogleApiActivity"
165-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f6fa56a75284f8e0ea5d27971291f441\transformed\jetified-play-services-base-18.5.0\AndroidManifest.xml:5:19-85
166            android:exported="false"
166-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f6fa56a75284f8e0ea5d27971291f441\transformed\jetified-play-services-base-18.5.0\AndroidManifest.xml:5:146-170
167            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
167-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f6fa56a75284f8e0ea5d27971291f441\transformed\jetified-play-services-base-18.5.0\AndroidManifest.xml:5:86-145
168
169        <meta-data
169-->[com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5568a35fadd1ad6d1c703f16a16e5540\transformed\jetified-play-services-basement-18.4.0\AndroidManifest.xml:6:9-122
170            android:name="com.google.android.gms.version"
170-->[com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5568a35fadd1ad6d1c703f16a16e5540\transformed\jetified-play-services-basement-18.4.0\AndroidManifest.xml:6:20-65
171            android:value="@integer/google_play_services_version" />
171-->[com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5568a35fadd1ad6d1c703f16a16e5540\transformed\jetified-play-services-basement-18.4.0\AndroidManifest.xml:6:66-119
172
173        <uses-library
173-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5093ab42d2307deb2d7ac0b7f5718c38\transformed\jetified-window-1.2.0\AndroidManifest.xml:23:9-25:40
174            android:name="androidx.window.extensions"
174-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5093ab42d2307deb2d7ac0b7f5718c38\transformed\jetified-window-1.2.0\AndroidManifest.xml:24:13-54
175            android:required="false" />
175-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5093ab42d2307deb2d7ac0b7f5718c38\transformed\jetified-window-1.2.0\AndroidManifest.xml:25:13-37
176        <uses-library
176-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5093ab42d2307deb2d7ac0b7f5718c38\transformed\jetified-window-1.2.0\AndroidManifest.xml:26:9-28:40
177            android:name="androidx.window.sidecar"
177-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5093ab42d2307deb2d7ac0b7f5718c38\transformed\jetified-window-1.2.0\AndroidManifest.xml:27:13-51
178            android:required="false" />
178-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5093ab42d2307deb2d7ac0b7f5718c38\transformed\jetified-window-1.2.0\AndroidManifest.xml:28:13-37
179
180        <provider
180-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d221757f531c672c2df2db4ebaa2cbf1\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:24:9-32:20
181            android:name="androidx.startup.InitializationProvider"
181-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d221757f531c672c2df2db4ebaa2cbf1\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:25:13-67
182            android:authorities="com.example.lc_mobile_app.androidx-startup"
182-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d221757f531c672c2df2db4ebaa2cbf1\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:26:13-68
183            android:exported="false" >
183-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d221757f531c672c2df2db4ebaa2cbf1\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:27:13-37
184            <meta-data
184-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d221757f531c672c2df2db4ebaa2cbf1\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:29:13-31:52
185                android:name="androidx.emoji2.text.EmojiCompatInitializer"
185-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d221757f531c672c2df2db4ebaa2cbf1\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:30:17-75
186                android:value="androidx.startup" />
186-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d221757f531c672c2df2db4ebaa2cbf1\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:31:17-49
187            <meta-data
187-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e80aa748ff6540407d8dba61c0d945fe\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:29:13-31:52
188                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
188-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e80aa748ff6540407d8dba61c0d945fe\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:30:17-78
189                android:value="androidx.startup" />
189-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e80aa748ff6540407d8dba61c0d945fe\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:31:17-49
190            <meta-data
190-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f7dcf6815bacc3ae7f099ba801348de\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:29:13-31:52
191                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
191-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f7dcf6815bacc3ae7f099ba801348de\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:30:17-85
192                android:value="androidx.startup" />
192-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f7dcf6815bacc3ae7f099ba801348de\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:31:17-49
193        </provider>
194
195        <receiver
195-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f7dcf6815bacc3ae7f099ba801348de\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:34:9-52:20
196            android:name="androidx.profileinstaller.ProfileInstallReceiver"
196-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f7dcf6815bacc3ae7f099ba801348de\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:35:13-76
197            android:directBootAware="false"
197-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f7dcf6815bacc3ae7f099ba801348de\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:36:13-44
198            android:enabled="true"
198-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f7dcf6815bacc3ae7f099ba801348de\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:37:13-35
199            android:exported="true"
199-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f7dcf6815bacc3ae7f099ba801348de\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:38:13-36
200            android:permission="android.permission.DUMP" >
200-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f7dcf6815bacc3ae7f099ba801348de\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:39:13-57
201            <intent-filter>
201-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f7dcf6815bacc3ae7f099ba801348de\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:40:13-42:29
202                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
202-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f7dcf6815bacc3ae7f099ba801348de\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:41:17-91
202-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f7dcf6815bacc3ae7f099ba801348de\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:41:25-88
203            </intent-filter>
204            <intent-filter>
204-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f7dcf6815bacc3ae7f099ba801348de\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:43:13-45:29
205                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
205-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f7dcf6815bacc3ae7f099ba801348de\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:44:17-85
205-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f7dcf6815bacc3ae7f099ba801348de\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:44:25-82
206            </intent-filter>
207            <intent-filter>
207-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f7dcf6815bacc3ae7f099ba801348de\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:46:13-48:29
208                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
208-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f7dcf6815bacc3ae7f099ba801348de\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:47:17-88
208-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f7dcf6815bacc3ae7f099ba801348de\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:47:25-85
209            </intent-filter>
210            <intent-filter>
210-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f7dcf6815bacc3ae7f099ba801348de\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:49:13-51:29
211                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
211-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f7dcf6815bacc3ae7f099ba801348de\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:50:17-95
211-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f7dcf6815bacc3ae7f099ba801348de\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:50:25-92
212            </intent-filter>
213        </receiver>
214
215        <service
215-->[com.google.android.datatransport:transport-backend-cct:2.3.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3fe589753a4aba9a36c72c795b877407\transformed\jetified-transport-backend-cct-2.3.3\AndroidManifest.xml:29:9-35:19
216            android:name="com.google.android.datatransport.runtime.backends.TransportBackendDiscovery"
216-->[com.google.android.datatransport:transport-backend-cct:2.3.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3fe589753a4aba9a36c72c795b877407\transformed\jetified-transport-backend-cct-2.3.3\AndroidManifest.xml:30:13-103
217            android:exported="false" >
217-->[com.google.android.datatransport:transport-backend-cct:2.3.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3fe589753a4aba9a36c72c795b877407\transformed\jetified-transport-backend-cct-2.3.3\AndroidManifest.xml:31:13-37
218            <meta-data
218-->[com.google.android.datatransport:transport-backend-cct:2.3.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3fe589753a4aba9a36c72c795b877407\transformed\jetified-transport-backend-cct-2.3.3\AndroidManifest.xml:32:13-34:39
219                android:name="backend:com.google.android.datatransport.cct.CctBackendFactory"
219-->[com.google.android.datatransport:transport-backend-cct:2.3.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3fe589753a4aba9a36c72c795b877407\transformed\jetified-transport-backend-cct-2.3.3\AndroidManifest.xml:33:17-94
220                android:value="cct" />
220-->[com.google.android.datatransport:transport-backend-cct:2.3.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3fe589753a4aba9a36c72c795b877407\transformed\jetified-transport-backend-cct-2.3.3\AndroidManifest.xml:34:17-36
221        </service>
222        <service
222-->[com.google.android.datatransport:transport-runtime:2.2.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9fb76887d9728a5f4c19fa6f805116ec\transformed\jetified-transport-runtime-2.2.6\AndroidManifest.xml:26:9-30:19
223            android:name="com.google.android.datatransport.runtime.scheduling.jobscheduling.JobInfoSchedulerService"
223-->[com.google.android.datatransport:transport-runtime:2.2.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9fb76887d9728a5f4c19fa6f805116ec\transformed\jetified-transport-runtime-2.2.6\AndroidManifest.xml:27:13-117
224            android:exported="false"
224-->[com.google.android.datatransport:transport-runtime:2.2.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9fb76887d9728a5f4c19fa6f805116ec\transformed\jetified-transport-runtime-2.2.6\AndroidManifest.xml:28:13-37
225            android:permission="android.permission.BIND_JOB_SERVICE" >
225-->[com.google.android.datatransport:transport-runtime:2.2.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9fb76887d9728a5f4c19fa6f805116ec\transformed\jetified-transport-runtime-2.2.6\AndroidManifest.xml:29:13-69
226        </service>
227
228        <receiver
228-->[com.google.android.datatransport:transport-runtime:2.2.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9fb76887d9728a5f4c19fa6f805116ec\transformed\jetified-transport-runtime-2.2.6\AndroidManifest.xml:32:9-34:40
229            android:name="com.google.android.datatransport.runtime.scheduling.jobscheduling.AlarmManagerSchedulerBroadcastReceiver"
229-->[com.google.android.datatransport:transport-runtime:2.2.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9fb76887d9728a5f4c19fa6f805116ec\transformed\jetified-transport-runtime-2.2.6\AndroidManifest.xml:33:13-132
230            android:exported="false" />
230-->[com.google.android.datatransport:transport-runtime:2.2.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9fb76887d9728a5f4c19fa6f805116ec\transformed\jetified-transport-runtime-2.2.6\AndroidManifest.xml:34:13-37
231    </application>
232
233</manifest>
