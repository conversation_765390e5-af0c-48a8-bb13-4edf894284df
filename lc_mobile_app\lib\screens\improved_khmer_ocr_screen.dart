import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:google_mlkit_text_recognition/google_mlkit_text_recognition.dart';
import 'package:image_picker/image_picker.dart';
import 'package:open_file/open_file.dart';
import 'package:path_provider/path_provider.dart';
import 'package:image/image.dart' as img;

class ImprovedKhmerOcrScreen extends StatefulWidget {
  const ImprovedKhmerOcrScreen({super.key});

  @override
  State<ImprovedKhmerOcrScreen> createState() => _ImprovedKhmerOcrScreenState();
}

class _ImprovedKhmerOcrScreenState extends State<ImprovedKhmerOcrScreen> {
  final TextRecognizer _textRecognizer = TextRecognizer(
    script: TextRecognitionScript.latin,
  );
  String _recognizedText = '';
  String _processedText = '';
  bool _isProcessing = false;
  String? _lastImagePath;
  String? _processedImagePath;
  final ImagePicker _picker = ImagePicker();

  @override
  void dispose() {
    _textRecognizer.close();
    super.dispose();
  }

  Future<void> _takePicture() async {
    setState(() {
      _isProcessing = true;
      _recognizedText = 'Processing...';
      _processedText = '';
    });

    try {
      final XFile? photo = await _picker.pickImage(
        source: ImageSource.camera,
        imageQuality: 100, // Use highest quality
        preferredCameraDevice: CameraDevice.rear,
      );

      if (photo != null) {
        _lastImagePath = photo.path;
        await _processImage(photo.path);
      } else {
        setState(() {
          _isProcessing = false;
          _recognizedText = 'No image captured';
        });
      }
    } catch (e) {
      setState(() {
        _isProcessing = false;
        _recognizedText = 'Error capturing image: $e';
      });
    }
  }

  Future<void> _pickImage() async {
    setState(() {
      _isProcessing = true;
      _recognizedText = 'Processing...';
      _processedText = '';
    });

    try {
      final XFile? image = await _picker.pickImage(
        source: ImageSource.gallery,
        imageQuality: 100, // Use highest quality
      );

      if (image != null) {
        _lastImagePath = image.path;
        await _processImage(image.path);
      } else {
        setState(() {
          _isProcessing = false;
          _recognizedText = 'No image selected';
        });
      }
    } catch (e) {
      setState(() {
        _isProcessing = false;
        _recognizedText = 'Error picking image: $e';
      });
    }
  }

  Future<void> _processImage(String imagePath) async {
    try {
      // Step 1: Pre-process the image
      final processedImagePath = await _preProcessImage(imagePath);
      _processedImagePath = processedImagePath;

      // Step 2: Perform OCR on the processed image
      final inputImage = InputImage.fromFilePath(processedImagePath);
      final RecognizedText recognizedText = await _textRecognizer.processImage(
        inputImage,
      );

      // Step 3: Post-process the recognized text
      final processedText = _postProcessText(recognizedText.text);

      setState(() {
        _recognizedText =
            recognizedText.text.isEmpty
                ? 'No text detected'
                : recognizedText.text;
        _processedText = processedText;
        _isProcessing = false;
      });
    } catch (e) {
      setState(() {
        _isProcessing = false;
        _recognizedText = 'Error processing image: $e';
      });
    }
  }

  Future<String> _preProcessImage(String imagePath) async {
    try {
      // Read the image file
      final File imageFile = File(imagePath);
      final Uint8List imageBytes = await imageFile.readAsBytes();
      final img.Image? originalImage = img.decodeImage(imageBytes);

      if (originalImage == null) {
        return imagePath; // Return original if decoding fails
      }

      // Apply image enhancements
      var processedImage = originalImage;

      // 1. Auto-contrast and brightness adjustment
      processedImage = img.adjustColor(
        processedImage,
        contrast: 1.3,
        brightness: 0.05,
        saturation: 1.2,
      );

      // 2. Convert to grayscale for better OCR
      processedImage = img.grayscale(processedImage);

      // 3. Apply additional contrast to grayscale image
      processedImage = img.adjustColor(processedImage, contrast: 1.2);

      // Save the processed image
      final tempDir = await getTemporaryDirectory();
      final tempPath =
          '${tempDir.path}/processed_image_${DateTime.now().millisecondsSinceEpoch}.jpg';
      final File processedFile = File(tempPath);
      await processedFile.writeAsBytes(
        img.encodeJpg(processedImage, quality: 100),
      );

      return tempPath;
    } catch (e) {
      // Log error or use a proper logging framework in production
      // For now, we'll just return the original image if processing fails
      return imagePath;
    }
  }

  String _postProcessText(String text) {
    if (text.isEmpty) return '';

    // Extract potential ID number using pattern matching
    final RegExp idNumberRegex = RegExp(r'\d{7,9}');
    final idMatches = idNumberRegex.allMatches(text);

    // Extract potential MRZ data (Machine Readable Zone)
    final RegExp mrzRegex = RegExp(r'[A-Z0-9<]{30,}');
    final mrzMatches = mrzRegex.allMatches(text);

    // Build structured result
    final StringBuffer result = StringBuffer();

    // Add ID number if found
    if (idMatches.isNotEmpty) {
      result.writeln('ID Number: ${idMatches.first.group(0)}');
    }

    // Add MRZ data if found
    if (mrzMatches.isNotEmpty) {
      final String mrz = mrzMatches.first.group(0) ?? '';
      result.writeln('MRZ: $mrz');

      // Try to extract name from MRZ
      if (mrz.contains('POV')) {
        result.writeln('Name: POV');
      }
    }

    return result.toString().isNotEmpty
        ? result.toString()
        : 'No structured data extracted';
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Improved Khmer OCR'),
        actions: [
          IconButton(
            icon: const Icon(Icons.photo_library),
            onPressed: _isProcessing ? null : _pickImage,
            tooltip: 'Pick from gallery',
          ),
        ],
      ),
      body: Column(
        children: [
          Expanded(
            flex: 3,
            child:
                _lastImagePath != null
                    ? Stack(
                      fit: StackFit.expand,
                      children: [
                        Image.file(
                          File(_processedImagePath ?? _lastImagePath!),
                          fit: BoxFit.contain,
                        ),
                        if (_isProcessing)
                          Container(
                            color: Colors.black.withAlpha(100),
                            child: const Center(
                              child: CircularProgressIndicator(),
                            ),
                          ),
                      ],
                    )
                    : Container(
                      color: Colors.black,
                      child: Center(
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Image.asset(
                              'assets/images/id_card_outline.png',
                              width: 200,
                              height: 126,
                              color: Colors.white.withAlpha(150),
                            ),
                            const SizedBox(height: 20),
                            const Text(
                              'Take a picture of a Cambodia ID card',
                              style: TextStyle(color: Colors.white),
                              textAlign: TextAlign.center,
                            ),
                          ],
                        ),
                      ),
                    ),
          ),
          Expanded(
            flex: 2,
            child: Container(
              width: double.infinity,
              padding: const EdgeInsets.all(16.0),
              color: Colors.white,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Recognized Text (Khmer):',
                    style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Expanded(
                    child: DefaultTabController(
                      length: 2,
                      child: Column(
                        children: [
                          const TabBar(
                            tabs: [
                              Tab(text: 'Raw Text'),
                              Tab(text: 'Processed'),
                            ],
                            labelColor: Colors.blue,
                          ),
                          Expanded(
                            child: TabBarView(
                              children: [
                                // Raw text tab
                                SingleChildScrollView(
                                  child: Container(
                                    width: double.infinity,
                                    padding: const EdgeInsets.all(12),
                                    decoration: BoxDecoration(
                                      color: Colors.grey[200],
                                      borderRadius: BorderRadius.circular(8),
                                    ),
                                    child: Text(
                                      _recognizedText,
                                      style: const TextStyle(fontSize: 16),
                                    ),
                                  ),
                                ),
                                // Processed text tab
                                SingleChildScrollView(
                                  child: Container(
                                    width: double.infinity,
                                    padding: const EdgeInsets.all(12),
                                    decoration: BoxDecoration(
                                      color: Colors.grey[200],
                                      borderRadius: BorderRadius.circular(8),
                                    ),
                                    child: Text(
                                      _processedText,
                                      style: const TextStyle(fontSize: 16),
                                    ),
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                  if (_lastImagePath != null)
                    Padding(
                      padding: const EdgeInsets.only(top: 8.0),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          ElevatedButton.icon(
                            onPressed: _isProcessing ? null : _takePicture,
                            icon: const Icon(Icons.camera_alt),
                            label: const Text('New Photo'),
                          ),
                          ElevatedButton.icon(
                            onPressed:
                                _isProcessing || _lastImagePath == null
                                    ? null
                                    : () {
                                      OpenFile.open(_lastImagePath!);
                                    },
                            icon: const Icon(Icons.image),
                            label: const Text('View Original'),
                          ),
                        ],
                      ),
                    ),
                ],
              ),
            ),
          ),
        ],
      ),
      floatingActionButton:
          _lastImagePath == null
              ? FloatingActionButton(
                onPressed: _isProcessing ? null : _takePicture,
                tooltip: 'Take Picture',
                child: const Icon(Icons.camera_alt),
              )
              : null,
    );
  }
}
