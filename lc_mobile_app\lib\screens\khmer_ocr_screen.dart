import 'dart:io';
import 'package:flutter/material.dart';
import 'package:google_mlkit_text_recognition/google_mlkit_text_recognition.dart';
import 'package:open_file/open_file.dart';
import 'package:image_picker/image_picker.dart';

class KhmerOcrScreen extends StatefulWidget {
  const KhmerOcrScreen({super.key});

  @override
  State<KhmerOcrScreen> createState() => _KhmerOcrScreenState();
}

class _KhmerOcrScreenState extends State<KhmerOcrScreen> {
  final TextRecognizer _textRecognizer = TextRecognizer(
    script: TextRecognitionScript.latin,
  );
  String _recognizedText = '';
  bool _isProcessing = false;
  String? _lastImagePath;
  final ImagePicker _picker = ImagePicker();

  @override
  void dispose() {
    _textRecognizer.close();
    super.dispose();
  }

  Future<void> _takePicture() async {
    setState(() {
      _isProcessing = true;
      _recognizedText = 'Processing...';
    });

    try {
      // Use ImagePicker to capture an image
      final XFile? photo = await _picker.pickImage(
        source: ImageSource.camera,
        imageQuality: 90,
      );

      if (photo != null) {
        _lastImagePath = photo.path;
        await _processImage(photo.path);
      } else {
        setState(() {
          _isProcessing = false;
          _recognizedText = 'No image captured';
        });
      }
    } catch (e) {
      setState(() {
        _isProcessing = false;
        _recognizedText = 'Error capturing image: $e';
      });
    }
  }

  Future<void> _pickImage() async {
    setState(() {
      _isProcessing = true;
      _recognizedText = 'Processing...';
    });

    try {
      final XFile? image = await _picker.pickImage(
        source: ImageSource.gallery,
        imageQuality: 90,
      );

      if (image != null) {
        _lastImagePath = image.path;
        await _processImage(image.path);
      } else {
        setState(() {
          _isProcessing = false;
          _recognizedText = 'No image selected';
        });
      }
    } catch (e) {
      setState(() {
        _isProcessing = false;
        _recognizedText = 'Error picking image: $e';
      });
    }
  }

  Future<void> _processImage(String imagePath) async {
    try {
      final inputImage = InputImage.fromFilePath(imagePath);
      final RecognizedText recognizedText = await _textRecognizer.processImage(
        inputImage,
      );

      setState(() {
        _recognizedText =
            recognizedText.text.isEmpty
                ? 'No text detected'
                : recognizedText.text;
        _isProcessing = false;
      });
    } catch (e) {
      setState(() {
        _isProcessing = false;
        _recognizedText = 'Error processing image: $e';
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Khmer OCR Scanner'),
        actions: [
          IconButton(
            icon: const Icon(Icons.photo_library),
            onPressed: _isProcessing ? null : _pickImage,
            tooltip: 'Pick from gallery',
          ),
        ],
      ),
      body: Column(
        children: [
          Expanded(
            flex: 3,
            child:
                _lastImagePath != null
                    ? Stack(
                      fit: StackFit.expand,
                      children: [
                        Image.file(File(_lastImagePath!), fit: BoxFit.contain),
                        if (_isProcessing)
                          Container(
                            color: Colors.black.withAlpha(100),
                            child: const Center(
                              child: CircularProgressIndicator(),
                            ),
                          ),
                      ],
                    )
                    : Container(
                      color: Colors.black,
                      child: const Center(
                        child: Text(
                          'Take a picture or select an image to scan for Khmer text',
                          style: TextStyle(color: Colors.white),
                          textAlign: TextAlign.center,
                        ),
                      ),
                    ),
          ),
          Expanded(
            flex: 2,
            child: Container(
              width: double.infinity,
              padding: const EdgeInsets.all(16.0),
              color: Colors.white,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Recognized Text (Khmer):',
                    style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Expanded(
                    child: SingleChildScrollView(
                      child: Container(
                        width: double.infinity,
                        padding: const EdgeInsets.all(12),
                        decoration: BoxDecoration(
                          color: Colors.grey[200],
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: Text(
                          _recognizedText,
                          style: const TextStyle(fontSize: 16),
                        ),
                      ),
                    ),
                  ),
                  if (_lastImagePath != null)
                    Padding(
                      padding: const EdgeInsets.only(top: 8.0),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          ElevatedButton.icon(
                            onPressed: _isProcessing ? null : _takePicture,
                            icon: const Icon(Icons.camera_alt),
                            label: const Text('New Photo'),
                          ),
                          ElevatedButton.icon(
                            onPressed:
                                _isProcessing || _lastImagePath == null
                                    ? null
                                    : () {
                                      OpenFile.open(_lastImagePath!);
                                    },
                            icon: const Icon(Icons.image),
                            label: const Text('View Image'),
                          ),
                        ],
                      ),
                    ),
                ],
              ),
            ),
          ),
        ],
      ),
      floatingActionButton:
          _lastImagePath == null
              ? FloatingActionButton(
                onPressed: _isProcessing ? null : _takePicture,
                tooltip: 'Take Picture',
                child: const Icon(Icons.camera_alt),
              )
              : null,
    );
  }
}
