import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:google_mlkit_text_recognition/google_mlkit_text_recognition.dart';
import '../models/id_card_data_model.dart';

class OcrService {
  final TextRecognizer _textRecognizer = TextRecognizer(
    script: TextRecognitionScript.latin,
  );

  // Process image and extract text
  Future<String> extractTextFromImage(File imageFile) async {
    try {
      final inputImage = InputImage.fromFile(imageFile);
      final RecognizedText recognizedText = await _textRecognizer.processImage(
        inputImage,
      );

      String text = recognizedText.text;
      return text;
    } catch (e) {
      debugPrint('Error extracting text: $e');
      return '';
    }
  }

  // Get all text blocks from the image
  Future<List<TextBlock>> getTextBlocks(File imageFile) async {
    try {
      final inputImage = InputImage.fromFile(imageFile);
      final RecognizedText recognizedText = await _textRecognizer.processImage(
        inputImage,
      );

      return recognizedText.blocks;
    } catch (e) {
      debugPrint('Error getting text blocks: $e');
      return [];
    }
  }

  // Parse ID card data from extracted text
  Future<IDCardData> extractIDCardData(File imageFile) async {
    try {
      final String extractedText = await extractTextFromImage(imageFile);
      final List<TextBlock> textBlocks = await getTextBlocks(imageFile);

      // Try to detect if this is a Cambodia ID card
      final bool isCambodianID = _isCambodianIDCard(extractedText);

      if (isCambodianID) {
        return _extractCambodianIDCardData(extractedText, textBlocks);
      } else {
        return _extractGenericIDCardData(extractedText);
      }
    } catch (e) {
      debugPrint('Error extracting ID card data: $e');
      return IDCardData.empty();
    }
  }

  // Check if the text is from a Cambodian ID card
  bool _isCambodianIDCard(String text) {
    // Look for specific Cambodian ID card markers
    final bool hasKhmerText = RegExp(
      r'[\u1780-\u17FF]',
    ).hasMatch(text); // Khmer Unicode range
    final bool hasIDKHM = text.contains('IDKHM') || text.contains('KHM');
    final bool hasCambodia =
        text.toLowerCase().contains('cambodia') ||
        text.toLowerCase().contains('cambodian');

    return hasKhmerText || hasIDKHM || hasCambodia;
  }

  // Extract data specifically from Cambodian ID cards
  IDCardData _extractCambodianIDCardData(
    String extractedText,
    List<TextBlock> textBlocks,
  ) {
    IDCardData idCardData = IDCardData.empty();

    // Extract ID number (format: IDKHM12345678...)
    final RegExp idRegex = RegExp(r'IDKHM([0-9]+)');
    final idMatch = idRegex.firstMatch(extractedText);
    if (idMatch != null && idMatch.groupCount >= 1) {
      idCardData = idCardData.copyWith(
        idNumber: 'IDKHM${idMatch.group(1)?.trim() ?? ''}',
      );
    } else {
      // Try alternative format with spaces or line breaks
      final RegExp altIdRegex = RegExp(r'ID\s*KHM\s*([0-9]+)');
      final altIdMatch = altIdRegex.firstMatch(extractedText);
      if (altIdMatch != null && altIdMatch.groupCount >= 1) {
        idCardData = idCardData.copyWith(
          idNumber: 'IDKHM${altIdMatch.group(1)?.trim() ?? ''}',
        );
      }
    }

    // Extract name - look for text near the top of the card after Khmer text
    // This is challenging due to Khmer script, so we'll use position heuristics
    String? name;
    for (final block in textBlocks) {
      // Skip Khmer text blocks
      if (RegExp(r'[\u1780-\u17FF]').hasMatch(block.text)) continue;

      // Look for name-like text (all caps, no numbers)
      if (block.text.toUpperCase() == block.text &&
          !RegExp(r'[0-9]').hasMatch(block.text) &&
          block.text.length > 3 &&
          !block.text.contains('KHM') &&
          !block.text.contains('IDKHM')) {
        name = block.text.trim();
        break;
      }
    }

    if (name != null) {
      idCardData = idCardData.copyWith(fullName: name);
    }

    // Extract date of birth (typically in format DD-MM-YYYY)
    final RegExp dobRegex = RegExp(
      r'(\d{1,2})[\-\.\/](\d{1,2})[\-\.\/](\d{4})',
    );
    final dobMatches = dobRegex.allMatches(extractedText);

    for (final match in dobMatches) {
      if (match.groupCount >= 3) {
        final day = match.group(1);
        final month = match.group(2);
        final year = match.group(3);

        // Validate as a reasonable date
        final int? yearInt = int.tryParse(year ?? '');
        if (yearInt != null && yearInt > 1900 && yearInt < 2100) {
          idCardData = idCardData.copyWith(dateOfBirth: '$day-$month-$year');
          break;
        }
      }
    }

    // Extract nationality (almost always "Cambodian" or "CAMBODIAN")
    idCardData = idCardData.copyWith(nationality: 'Cambodian');

    // Extract gender based on common patterns in Cambodian IDs
    final RegExp maleRegex = RegExp(r'\bM\b|\bMALE\b', caseSensitive: false);
    final RegExp femaleRegex = RegExp(
      r'\bF\b|\bFEMALE\b',
      caseSensitive: false,
    );

    if (maleRegex.hasMatch(extractedText)) {
      idCardData = idCardData.copyWith(gender: 'Male');
    } else if (femaleRegex.hasMatch(extractedText)) {
      idCardData = idCardData.copyWith(gender: 'Female');
    }

    // Extract address - this is challenging due to Khmer script
    // We'll look for text blocks that might contain address information
    for (final block in textBlocks) {
      // Skip very short blocks or blocks with ID numbers
      if (block.text.length < 5 || RegExp(r'IDKHM').hasMatch(block.text)) {
        continue;
      }

      // Look for blocks with mixed text that might be addresses
      if (!RegExp(r'[\u1780-\u17FF]').hasMatch(block.text) && // Not Khmer
          block.text.contains(RegExp(r'[a-zA-Z]')) && // Has Latin letters
          !block.text.toUpperCase().contains('KHM') && // Not the country code
          !block.text.contains(RegExp(r'^\d+$'))) {
        // Not just numbers

        // Check if it's not already captured as name
        if (block.text.trim() != idCardData.fullName) {
          idCardData = idCardData.copyWith(address: block.text.trim());
          break;
        }
      }
    }

    return idCardData;
  }

  // Extract data from generic ID cards
  IDCardData _extractGenericIDCardData(String extractedText) {
    IDCardData idCardData = IDCardData.empty();

    // Extract ID number (assuming format like "ID: 123456789")
    final RegExp idRegex = RegExp(r'ID[:\s]*([0-9]+)');
    final idMatch = idRegex.firstMatch(extractedText);
    if (idMatch != null && idMatch.groupCount >= 1) {
      idCardData = idCardData.copyWith(
        idNumber: idMatch.group(1)?.trim() ?? '',
      );
    }

    // Extract name (assuming format like "Name: John Doe")
    final RegExp nameRegex = RegExp(r'Name[:\s]*([\w\s]+)');
    final nameMatch = nameRegex.firstMatch(extractedText);
    if (nameMatch != null && nameMatch.groupCount >= 1) {
      idCardData = idCardData.copyWith(
        fullName: nameMatch.group(1)?.trim() ?? '',
      );
    }

    // Extract date of birth (assuming format like "DOB: DD/MM/YYYY")
    final RegExp dobRegex = RegExp(
      r'(DOB|Date of Birth)[:\s]*(\d{1,2}[\/\-\.]\d{1,2}[\/\-\.]\d{2,4})',
    );
    final dobMatch = dobRegex.firstMatch(extractedText);
    if (dobMatch != null && dobMatch.groupCount >= 2) {
      idCardData = idCardData.copyWith(
        dateOfBirth: dobMatch.group(2)?.trim() ?? '',
      );
    }

    // Extract address (assuming format like "Address: 123 Main St")
    final RegExp addressRegex = RegExp(r'Address[:\s]*([\w\s,\.]+)');
    final addressMatch = addressRegex.firstMatch(extractedText);
    if (addressMatch != null && addressMatch.groupCount >= 1) {
      idCardData = idCardData.copyWith(
        address: addressMatch.group(1)?.trim() ?? '',
      );
    }

    // Extract gender (assuming format like "Gender: Male")
    final RegExp genderRegex = RegExp(r'Gender[:\s]*(Male|Female|M|F)');
    final genderMatch = genderRegex.firstMatch(extractedText);
    if (genderMatch != null && genderMatch.groupCount >= 1) {
      idCardData = idCardData.copyWith(gender: genderMatch.group(1)?.trim());
    }

    // Extract nationality (assuming format like "Nationality: Cambodian")
    final RegExp nationalityRegex = RegExp(r'Nationality[:\s]*([\w\s]+)');
    final nationalityMatch = nationalityRegex.firstMatch(extractedText);
    if (nationalityMatch != null && nationalityMatch.groupCount >= 1) {
      idCardData = idCardData.copyWith(
        nationality: nationalityMatch.group(1)?.trim(),
      );
    }

    return idCardData;
  }

  // Dispose resources
  void dispose() {
    _textRecognizer.close();
  }
}
